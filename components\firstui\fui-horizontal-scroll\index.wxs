function scroll(event, ownerInstance) {
	var detail = event.detail
	var scrollWidth = detail.scrollWidth
	var scrollLeft = detail.scrollLeft
	var dataset = event.currentTarget.dataset
	var width = dataset.width || 0
	var scrollBarWidth = dataset.bgwidth || 0
	var blockWidth = dataset.blockwidth || 0
	var x = scrollLeft / (scrollWidth - width) * (scrollBarWidth - blockWidth)
	setBarStyle(ownerInstance, x)
}

function scrolltolower(event, ownerInstance) {
	ownerInstance.callMethod('scrollEvent', 'right')
	var dataset = event.currentTarget.dataset
	var scrollBarWidth = dataset.bgwidth || 0
	var blockWidth = dataset.blockwidth || 0
	setBarStyle(ownerInstance, scrollBarWidth - blockWidth)
}

function scrolltoupper(event, ownerInstance) {
	ownerInstance.callMethod('scrollEvent', 'left')
	setBarStyle(ownerInstance, 0)
}

function setBarStyle(ownerInstance, x) {
	var block = ownerInstance.selectComponent('.fui-hor__scroll-indicator')
	block && block.setStyle({
		transform: 'translate3d(' + x + 'px,0,0)'
	})

}

module.exports = {
	scroll: scroll,
	scrolltolower: scrolltolower,
	scrolltoupper: scrolltoupper
}