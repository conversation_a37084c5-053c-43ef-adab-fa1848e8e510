<template>
	<view class="lui-upload" v-if="!disabled">
		<view v-if="showUploadList" class="lui-list-item lui-preview-wrap" v-for="(item, index) in lists" :key="index"
			:style="{width: addUnit(width),height: addUnit(height)}">
			<view v-if="deletable" class="lui-delete-icon" @tap.stop="deleteItem(index)"
				:style="{background: delBgColor}">
				<fui-icon :name="delIcon" :color="delColor" size="38"></fui-icon>
			</view>
			<lui-line-progress v-if="showProgress && item.progress > 0 && !item.error" :show-percent="false" height="16"
				class="lui-progress" :percent="item.progress" active-color="rgb(47, 125, 205)">
			</lui-line-progress>
			<view @tap.stop="retry(index)" v-if="item.error" class="lui-error-btn">点击重试</view>
			<image @tap.stop="doPreviewImage(item.url || item.path, index)" class="lui-preview-image"
				v-if="!item.isImage" :src="item.url || item.path" :mode="imageMode">
			</image>
		</view>
		<view style="display: inline-block;" @tap="selectFile" v-if="maxCount > lists.length">
			<slot name="addBtn"></slot>
			<view v-if="!customBtn" class="lui-list-item lui-add-wrap" hover-class="lui-add-wrap__hover"
				hover-stay-time="150" :style="{width: addUnit(width),height: addUnit(height)}"
				style="background-color:#F2F4F5 ;border: 2rpx dashed #DDDDDD;;">
				<fui-icon name="plus" color="#B1B5B8" size="80"></fui-icon>
				<view class="lui-add-tips" style="color: #B1B5B8;font-size: 20rpx;" v-if="uploadText">{{ uploadText }}</view>
			</view>
		</view>
		<view v-if="showUploadList" v-for="(item) in 4" :key="item" :style="{width: addUnit(width),height: 0}">
		</view>
	</view>
</template>

<script setup>
	import validation from '@/utils/validation.js';
	import {
		baseApiURL
	} from "@/config/dns.js";
	import {
		ref,
		watch
	} from "vue";
	const emits = defineEmits(['on-remove', 'on-preview', 'on-oversize', 'on-exceed', 'on-choose-complete',
		'on-choose-fail', 'on-error', 'on-uploaded', 'on-success', 'on-change', 'on-progress', 'on-list-change'
	])
	const props = defineProps({
		// 上传的文件字段名
		name: {
			type: String,
			default: 'file'
		},
		// 后端地址
		action: {
			type: String,
			default: baseApiURL + "/api/upload/images"
		},
		// 额外携带的参数
		formData: {
			type: Object,
			default () {
				return {};
			}
		},
		// 头部信息
		header: {
			type: Object,
			default () {
				return {};
			}
		},
		// 如果上传后的返回值为json字符串，是否自动转json
		toJson: {
			type: Boolean,
			default: true
		},
		// 内部预览图片区域和选择图片按钮的区域宽度
		width: {
			type: [String, Number],
			default: 200
		},
		// 内部预览图片区域和选择图片按钮的区域高度
		height: {
			type: [String, Number],
			default: 200
		},
		//是否显示组件自带的图片预览功能
		showUploadList: {
			type: Boolean,
			default: true
		},
		// 是否启用
		disabled: {
			type: Boolean,
			default: false
		},
		//  是否显示进度条
		showProgress: {
			type: Boolean,
			default: true
		},
		// 是否显示toast消息提示
		showTips: {
			type: Boolean,
			default: true
		},
		// 是否展示删除按钮
		deletable: {
			type: Boolean,
			default: true
		},
		// 右上角关闭按钮的背景颜色
		delBgColor: {
			type: String,
			default: '#fa3534'
		},
		// 右上角关闭按钮的叉号图标的颜色
		delColor: {
			type: String,
			default: '#ffffff'
		},
		// 右上角删除图标名称，只能为uView内置图标
		delIcon: {
			type: String,
			default: 'close'
		},
		// 删除照片时是否显示模态框
		showDelModel: {
			type: Boolean,
			default: true
		},
		// 移除文件前的钩子
		beforeRemove: {
			type: Function,
			default: null
		},
		// 在各个回调事件中的最后一个参数返回，用于区别是哪一个组件的事件
		index: {
			type: [Number, String],
			default: ''
		},
		sort: {
			type: [Number, String],
			default: ''
		},
		// 是否在点击预览图后展示全屏图片预览
		previewFullImage: {
			type: Boolean,
			default: true
		},
		// 预览上传的图片时的裁剪模式，和image组件mode属性一致
		imageMode: {
			type: String,
			default: 'aspectFill'
		},
		// 最大上传数量
		maxCount: {
			type: [String, Number],
			default: 1
		},
		// 是否开启图片多选，部分安卓机型不支持
		multiple: {
			type: Boolean,
			default: true
		},
		sourceType: {
			type: Array,
			default () {
				return ['album', 'camera'];
			}
		},
		// 所选的图片的尺寸, 可选值为original compressed
		sizeType: {
			type: Array,
			default () {
				return ['original', 'compressed'];
			}
		},
		// 允许上传的图片后缀
		limitType: {
			type: Array,
			default () {
				// 支付宝小程序真机选择图片的后缀为"image"
				// https://opendocs.alipay.com/mini/api/media-image
				return ['png', 'jpg', 'jpeg', 'webp', 'gif', 'image'];
			}
		},
		// 是否自动上传
		autoUpload: {
			type: Boolean,
			default: true
		},
		// 上传区域的提示文字
		uploadText: {
			type: String,
			default: '选择图片'
		},
		// 是否通过slot自定义传入选择图标的按钮
		customBtn: {
			type: Boolean,
			default: false
		},
		// 文件大小限制，单位为byte
		maxSize: {
			type: [String, Number],
			default: Number.MAX_VALUE
		},
		// 上传前的钩子，每个文件上传前都会执行
		beforeUpload: {
			type: Function,
			default: null
		},
		// 显示已上传的文件列表
		fileList: {
			type: Array,
			default () {
				return [];
			}
		},
	});

	const lists = ref([]);
	const uploading = ref(false);

	watch(() => props.fileList, (val) => {
		val.map(value => {
			// 首先检查内部是否已经添加过这张图片，因为外部绑定了一个对象给fileList的话(对象引用)，进行修改外部fileList
			// 时，会触发watch，导致重新把原来的图片再次添加到lists
			// 数组的some方法意思是，只要数组元素有任意一个元素条件符合，就返回true，而另一个数组的every方法的意思是数组所有元素都符合条件才返回true
			let tmp = lists.value.some(val => val.url == value.url);
			// 如果内部没有这个图片(tmp为false)，则添加到内部
			!tmp && lists.value.push({
				url: value.url,
				path: value.path,
				error: false,
				progress: 100
			});
		});
	}, {
		immediate: true
	});

	// 监听lists的变化，发出事件
	watch(() => lists.value, (n) => {
		emits('on-list-change', n, props.index);
	});

	// 添加单位，如果有rpx，%，px等单位结尾或者值为auto，直接返回，否则加上rpx单位结尾
	const addUnit = (value = 'auto', unit = 'rpx') => {
		value = String(value);
		// 用uView内置验证规则中的number判断是否为数值
		return validation.number(value) ? `${value}${unit}` : value;
	}

	// 提示用户消息
	const showToast = (message, force = false) => {
		if (props.showTips || force) {
			uni.showToast({
				title: message,
				icon: 'none'
			});
		}
	};
	const setList = (list) => {
		lists.value = list
	}
	// 删除一个图片
	const deleteItem = (index) => {
		if (props.showDelModel) {
			uni.showModal({
				title: '提示',
				content: '您确定要删除此项吗？',
				success: (res) => {
					if (res.confirm) {
						confirmDeleteItem(index);
					}
				}
			});
		} else {
			confirmDeleteItem(index);
		}
	}

	const confirmDeleteItem = async (index) => {
		// 先检查是否有定义before-remove移除前钩子
		// 执行before-remove钩子
		if (props.beforeRemove && typeof(props.beforeRemove) === 'function') {
			// 此处钩子执行 原理同before-remove参数，见上方注释
			let beforeResponse = props.beforeRemove(index, lists.value);
			// 判断是否返回了promise
			if (!!beforeResponse && typeof beforeResponse.then === 'function') {
				await beforeResponse.then(res => {
					// promise返回成功，不进行动作，继续删除
					handlerDeleteItem(index);
				}).catch(err => {
					// 如果进入promise的reject，终止删除操作
					showToast('已终止移除');
				})
			} else if (beforeResponse === false) {
				// 返回false，终止删除
				showToast('已终止移除');
			} else {
				// 如果返回true，执行删除操作
				handlerDeleteItem(index);
			}
		} else {
			// 如果不存在before-remove钩子，
			handlerDeleteItem(index);
		}
	}

	// 执行移除图片的动作，上方代码只是判断是否可以移除
	const handlerDeleteItem = (index) => {
		// 如果文件正在上传中，终止上传任务，进度在0 < progress < 100则意味着正在上传
		if (lists.value[index].process < 100 && lists.value[index].process > 0) {
			typeof lists.value[index].uploadTask != 'undefined' && lists.value[index].uploadTask.abort();
		}
		lists.value.splice(index, 1);
		// this.$forceUpdate(); 未知方法
		emits('on-remove', index, lists.value, props.index)
		showToast('移除成功');
	};

	// 对失败的图片重新上传
	const retry = (index) => {
		lists.value[index].progress = 0;
		lists.value[index].error = false;
		lists.value[index].response = null;
		uni.showLoading({
			title: '重新上传'
		});
		uploadFile(index);
	};

	// 预览图片
	const doPreviewImage = (url, index) => {
		if (!props.previewFullImage) return;
		const images = lists.value.map(item => item.url || item.path);
		uni.previewImage({
			urls: images,
			current: url,
			success: () => {
				emits('on-preview', url, lists.value, props.index);
			},
			fail: () => {
				uni.showToast({
					title: '预览图片失败',
					icon: 'none'
				});
			}
		});
	};

	// 选择图片
	const selectFile = () => {
		if (props.disabled) return;
		const {
			maxCount,
			multiple,
			sourceType,
			sizeType,
			maxSize
		} = props;
		const newMaxCount = maxCount - lists.value.length;
		// 设置为只选择图片的时候使用 chooseImage 来实现
		let chooseFile = new Promise((resolve, reject) => {
			uni.chooseImage({
				count: multiple ? (newMaxCount > 9 ? 9 : newMaxCount) : 1,
				sourceType: sourceType,
				sizeType,
				success: resolve,
				fail: reject
			});
		});
		chooseFile.then(res => {
			let file = null;
			let listOldLength = lists.value.length;
			res.tempFiles.map((val, index) => {
				// 检查文件后缀是否允许，如果不在limitType内，就会返回false
				if (!checkFileExt(val)) return;
				// 如果是非多选，index大于等于1或者超出最大限制数量时，不处理
				if (!multiple && index >= 1) return;
				if (val.size > maxSize) {
					emits('on-oversize', val, lists.value, props.index);
					showToast('超出允许的文件大小');
				} else {
					if (maxCount <= lists.value.length) {
						emits('on-exceed', val, lists.value, props.index);
						showToast('超出最大允许的文件个数');
						return;
					}
					lists.value.push({
						url: val.path,
						progress: 0,
						error: false,
						file: val
					});
				}
			});
			// 每次图片选择完，抛出一个事件，并将当前内部选择的图片数组抛出去
			emits('on-choose-complete', lists.value, props.index);
			if (props.autoUpload) uploadFile(listOldLength);
		}).catch(error => {
			console.log(error)
			emits('on-choose-fail', error);
		});
	}

	// 判断文件后缀是否允许
	const checkFileExt = (file) => {
		// 检查是否在允许的后缀中
		let noArrowExt = false;
		// 获取后缀名
		let fileExt = '';
		const reg = /.+\./;
		// 如果是H5，需要从name中判断
		// #ifdef H5
		fileExt = file.name.replace(reg, "").toLowerCase();
		// #endif
		// 非H5，需要从path中读取后缀
		// #ifndef H5
		fileExt = file.path.replace(reg, "").toLowerCase();
		// #endif
		// 使用数组的some方法，只要符合limitType中的一个，就返回true
		noArrowExt = props.limitType.some(ext => {
			// 转为小写
			return ext.toLowerCase() === fileExt;
		})
		if (!noArrowExt) showToast(`不允许选择${fileExt}格式的文件`);
		return noArrowExt;
	}

	const uploadFile = async (index = 0) => {
		if (props.disabled) return;
		if (uploading.value) return;
		// 全部上传完成
		if (index >= lists.value.length) {
			emits('on-uploaded', lists.value, props.index);
			return;
		}
		// 检查是否是已上传或者正在上传中
		if (lists.value[index].progress == 100) {
			if (props.autoUpload == false) uploadFile(index + 1);
			return;
		}

		// 执行before-upload钩子
		if (props.beforeUpload && typeof(props.beforeUpload) === 'function') {
			let beforeResponse = props.beforeUpload(index, lists.value);
			// 判断是否返回了promise
			if (!!beforeResponse && typeof beforeResponse.then === 'function') {
				await beforeResponse.then(res => {
					// promise返回成功，不进行动作，继续上传
				}).catch(err => {
					// 进入catch回调的话，继续下一张
					return uploadFile(index + 1);
				})
			} else if (beforeResponse === false) {
				// 如果返回false，继续下一张图片的上传
				return uploadFile(index + 1);
			} else {
				// 此处为返回"true"的情形，这里不写代码，就跳过此处，继续执行当前的上传逻辑
			}
		}

		// 检查上传地址
		if (!props.action) {
			showToast('请配置上传地址', true);
			return;
		}

		lists.value[index].error = false;
		uploading.value = true;
		// 创建上传对象
		const task = uni.uploadFile({
			url: props.action,
			filePath: lists.value[index].url,
			name: props.name,
			formData: props.formData,
			header: props.header,
			success: res => {
				// 判断是否json字符串，将其转为json格式
				let data = props.toJson && validation.jsonString(res.data) ? JSON.parse(res.data) : res
					.data;

				if (![200, 201, 204].includes(res.statusCode) || data.code == 0) {
					uploadError(index, data);
				} else {
					// 上传成功
					lists.value[index].response = data;
					lists.value[index].url = baseApiURL + data.data.url;
					lists.value[index].path = baseApiURL + data.data.path;
					lists.value[index].progress = 100;
					lists.value[index].error = false;
					emits('on-success', data, index, lists.value, props.index);
				}
			},
			fail: e => {
				uploadError(index, e);
			},
			complete: res => {
				uni.hideLoading();
				uploading.value = false;
				uploadFile(index + 1);
				emits('on-change', res, index, lists.value, props.index);
			}
		});
		task.onProgressUpdate(res => {
			if (res.progress > 0) {
				lists.value[index].progress = res.progress;
				emits('on-progress', res, index, lists.value, props.index);
			}
		});
	}
	//清空数据
	const clear = () => {
		lists.value = []
	}
	// 上传失败
	const uploadError = (index, err) => {
		lists.value[index].progress = 0;
		lists.value[index].error = true;
		lists.value[index].response = null;
		emits('on-error', err, index, lists.value, props.index);
		showToast('上传失败，请重试');
	};

	// 该方法供用户通过ref调用，手动上传
	const upload = () => {
		uploadFile();
	};

	// 用户通过ref手动的形式，移除一张图片
	const remove = (index) => {
		// 判断索引的合法范围
		if (index >= 0 && index < lists.value.length) {
			lists.splice(index, 1);
			emits('on-list-change', lists.value, props.index);
		}
	};

	// 用户通过ref获取文件数量
	const getNum = () => {
		return lists.value.length;
	};


	defineExpose({
		upload: upload,
		remove: remove,
		getNum: getNum,
		setList: setList
	})
</script>

<style lang="less" scoped>
	.lui-upload {
		display: flex;
		flex-wrap: wrap;
		align-items: center;
		justify-content: space-between;

		.lui-list-item {
			width: 200rpx;
			height: 200rpx;
			overflow: hidden;
			background: rgb(244, 245, 246);
			position: relative;
			border-radius: 10rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			margin-bottom: 12rpx;

			.lui-delete-icon {
				position: absolute;
				top: 10rpx;
				right: 10rpx;
				z-index: 10;
				border-radius: 100rpx;
				width: 44rpx;
				height: 44rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.lui-error-btn {
				color: #ffffff;
				// background-color: $u-type-error;
				font-size: 20rpx;
				padding: 4px 0;
				text-align: center;
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				z-index: 9;
				line-height: 1;
			}

			.lui-preview-image {
				display: block;
				width: 100%;
				height: 100%;
				border-radius: 10rpx;
			}

		}

		.lui-preview-wrap {
			border: 1px solid rgb(235, 236, 238);
		}

		.lui-add-wrap {
			flex-direction: column;
			font-size: 26rpx;
		}

		.lui-add-wrap__hover {
			background-color: rgb(235, 236, 238);
		}

		.lui-add-tips {
			margin-top: 20rpx;
			line-height: 40rpx;
		}

		.lui-progress {
			position: absolute;
			bottom: 10rpx;
			left: 8rpx;
			right: 8rpx;
			z-index: 9;
			width: auto;
		}

	}
</style>