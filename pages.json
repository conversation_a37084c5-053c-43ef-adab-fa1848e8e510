{
	"easycom": {
		"autoscan": true,
		"custom": {
			"fui-(.*)": "@/components/firstui/fui-$1/fui-$1.vue",
			"lui-(.*)": "@/components/lui/lui-$1/lui-$1.vue",
			"sx-svg": "@/components/sx-svg/sx-svg.vue"
		}
	},
	"pages": [ //pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
		{
			"path": "pages/home/<USER>",
			"style": {
				"navigationBarTitleText": "记录",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/statistics/index",
			"style": {
				"navigationBarTitleText": "统计",
				"navigationStyle": "custom"
			}
		},
		{
			"path": "pages/profile/index",
			"style": {
				"navigationBarTitleText": "我的",
				"navigationStyle": "custom"
			}
		}
	],
	"subPackages": [
		
	],
	"preloadRule": {},
	"globalStyle": {
		"navigationBarTextStyle": "black",
		"navigationBarTitleText": "蓉蓉の小账本",
		"navigationBarBackgroundColor": "#F8F8F8",
		"backgroundColor": "#F8F8F8"
	},
	"uniIdRouter": {},
	"mp-weixin": {
	    "sharing": true
	},
	"tabBar": {
		"color": "#999999",
		"selectedColor": "#3b82f6",
		"borderStyle": "black",
		"backgroundColor": "#ffffff",
		"list": [
			{
				"pagePath": "pages/home/<USER>",
				"text": "记录",
				"iconPath": "/static/tabbar/fa--calendar-check-o.png",
				"selectedIconPath": "/static/tabbar/fa--calendar-check-o-A.png"
			},
			{
				"pagePath": "pages/statistics/index",
				"text": "统计",
				"iconPath": "/static/tabbar/fa--bar-chart.png",
				"selectedIconPath": "/static/tabbar/fa--bar-chart-A.png"
			},
			{
				"pagePath": "pages/profile/index",
				"text": "我的",
				"iconPath": "/static/tabbar/fa--user.png",
				"selectedIconPath": "/static/tabbar/fa--user-A.png"
			}
		]
	}
}
