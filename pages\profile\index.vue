<template>
	<view>
		<!-- 公共模块 -->
		<publicModule></publicModule>
		
		<!-- 状态栏 -->
		<fui-status-bar></fui-status-bar>
		
		<!-- 个人中心页面内容 -->
		<view class="profile-container">
			<!-- 个人信息头部 -->
			<view class="profile-header">
				<view class="header-top">
					<view class="header-title">个人中心</view>
					<view class="edit-btn" @click="showEditModal">
						<fui-icon custom-prefix="iconfont" name="icon-wenbenshuru" size="28" color="#ffffff"></fui-icon>
						<text class="edit-text">编辑</text>
					</view>
				</view>

				<view class="user-info">
					<view class="avatar-container">
						<image 
							class="user-avatar" 
							:src="userInfo.avatar" 
							mode="aspectFill"
						></image>
					</view>
					<view class="user-details">
						<view class="user-name">{{ userInfo.nickname }}</view>
						<view class="user-desc">记账达人 · 专注工作</view>
						<view class="user-meta">
							<view class="meta-item">
								<fui-icon custom-prefix="iconfont" name="icon-yly_jisuanqi" size="24" color="rgba(255,255,255,0.9)"></fui-icon>
								<text class="meta-text">{{ userInfo.phone }}</text>
							</view>
							<view class="meta-item">
								<fui-icon custom-prefix="iconfont" name="icon-yly_jisuanqi" size="24" color="rgba(255,255,255,0.9)"></fui-icon>
								<text class="meta-text">注册 {{ userInfo.registerDays }} 天</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 统计概览 -->
			<view class="stats-overview">
				<view v-for="stat in userStats" :key="stat.id" class="stat-card">
					<text class="stat-value" :style="{ color: stat.color }">{{ stat.value }}</text>
					<text class="stat-label">{{ stat.label }}</text>
				</view>
			</view>

			<!-- 时薪设置 -->
			<view class="hourly-rate-section">
				<view class="rate-card">
					<view class="rate-left">
						<view class="rate-icon">
							<fui-icon custom-prefix="iconfont" name="icon-renminbifuhao" size="32" color="#10b981"></fui-icon>
						</view>
						<view class="rate-info">
							<text class="rate-title">当前时薪</text>
							<text class="rate-desc">影响工时收入计算</text>
						</view>
					</view>
					<view class="rate-right">
						<text class="rate-amount">¥{{ userInfo.hourlyRate }}</text>
						<text class="rate-unit">/小时</text>
					</view>
				</view>
			</view>

			<!-- 功能设置 -->
			<view class="function-settings">
				<view class="section-title">
					<fui-icon custom-prefix="iconfont" name="icon-chilun" size="32" color="#3b82f6"></fui-icon>
					<text class="title-text">功能设置</text>
				</view>
				<view class="settings-list">
					<view v-for="item in functionSettings" :key="item.id" class="setting-item" @click="handleSettingClick(item)">
						<view class="setting-left">
							<view class="setting-icon" :style="{ backgroundColor: item.iconBg }">
								<fui-icon custom-prefix="iconfont" name="icon-yly_jisuanqi" size="32" :color="item.iconColor"></fui-icon>
							</view>
							<view class="setting-info">
								<text class="setting-title">{{ item.title }}</text>
								<text class="setting-desc">{{ item.description }}</text>
							</view>
						</view>
						<view class="setting-right">
							<view v-if="item.badge" class="setting-badge">{{ item.badge }}</view>
							<fui-icon custom-prefix="iconfont" name="icon-xiangyoujiantou" size="28" color="#9ca3af"></fui-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 应用设置 -->
			<view class="app-settings">
				<view class="section-title">
					<fui-icon custom-prefix="iconfont" name="icon-yly_jisuanqi" size="32" color="#8b5cf6"></fui-icon>
					<text class="title-text">应用设置</text>
				</view>
				<view class="settings-list">
					<view v-for="item in appSettings" :key="item.id" class="setting-item" @click="handleAppSettingClick(item)">
						<view class="setting-left">
							<view class="setting-icon" :style="{ backgroundColor: item.iconBg }">
								<fui-icon custom-prefix="iconfont" name="icon-yly_jisuanqi" size="32" :color="item.iconColor"></fui-icon>
							</view>
							<view class="setting-info">
								<text class="setting-title">{{ item.title }}</text>
								<text class="setting-desc">{{ item.description }}</text>
							</view>
						</view>
						<view class="setting-right">
							<fui-switch
								v-if="item.type === 'switch'"
								v-model="item.value"
								:scaleRatio="0.8"
								@change="handleSwitchChange(item)"
							></fui-switch>
							<fui-icon v-else custom-prefix="iconfont" name="icon-xiangyoujiantou" size="28" color="#9ca3af"></fui-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 更多功能 -->
			<view class="more-functions">
				<view class="section-title">
					<fui-icon custom-prefix="iconfont" name="icon-yly_jisuanqi" size="32" color="#ef4444"></fui-icon>
					<text class="title-text">更多</text>
				</view>
				<view class="settings-list">
					<view v-for="item in moreSettings" :key="item.id" class="setting-item" @click="handleMoreClick(item)">
						<view class="setting-left">
							<view class="setting-icon" :style="{ backgroundColor: item.iconBg }">
								<fui-icon custom-prefix="iconfont" name="icon-yly_jisuanqi" size="32" :color="item.iconColor"></fui-icon>
							</view>
							<view class="setting-info">
								<text class="setting-title">{{ item.title }}</text>
								<text class="setting-desc">{{ item.description }}</text>
							</view>
						</view>
						<view class="setting-right">
							<fui-icon custom-prefix="iconfont" name="icon-xiangyoujiantou" size="28" color="#9ca3af"></fui-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 退出登录 -->
			<view class="logout-section">
				<fui-button 
					:width="'100%'" 
					:height="'48'" 
					:size="'16'" 
					:bold="true"
					background="#fef2f2"
					color="#dc2626"
					:radius="'12'"
					:border="true"
					borderColor="#fecaca"
					@click="handleLogout"
				>
					<view class="logout-btn-content">
						<fui-icon custom-prefix="iconfont" name="icon-yly_jisuanqi" size="32" color="#dc2626"></fui-icon>
						<text class="logout-text">退出登录</text>
					</view>
				</fui-button>
			</view>

			<!-- 底部安全区域 -->
			<view class="bottom-safe"></view>
		</view>

		<!-- 编辑用户信息弹窗 -->
		<fui-modal 
			:show="showEditUserModal" 
			:maskClosable="true"
			:radius="'16'"
			@cancel="hideEditModal"
		>
			<view class="edit-modal">
				<view class="modal-header">
					<text class="modal-title">编辑个人信息</text>
					<view class="close-btn" @click="hideEditModal">
						<fa-icon name="times" size="14" color="#9ca3af"></fa-icon>
					</view>
				</view>
				
				<view class="modal-content">
					<view class="avatar-edit">
						<view class="avatar-preview">
							<image class="preview-image" :src="editForm.avatar" mode="aspectFill"></image>
							<view class="camera-btn" @click="changeAvatar">
								<fa-icon name="camera" size="12" color="#ffffff"></fa-icon>
							</view>
						</view>
					</view>
					
					<view class="input-group">
						<text class="input-label">昵称</text>
						<fui-input 
							v-model="editForm.nickname"
							placeholder="请输入昵称"
							:background="'#ffffff'"
							:radius="'8'"
							:border="true"
							borderColor="#e5e7eb"
						></fui-input>
					</view>
					
					<view class="input-group">
						<text class="input-label">手机号</text>
						<fui-input 
							v-model="editForm.phone"
							type="tel"
							placeholder="请输入手机号"
							:background="'#ffffff'"
							:radius="'8'"
							:border="true"
							borderColor="#e5e7eb"
						></fui-input>
					</view>
					
					<view class="input-group">
						<text class="input-label">时薪设置</text>
						<view class="hourly-input">
							<text class="currency">¥</text>
							<fui-input 
								v-model="editForm.hourlyRate"
								type="digit"
								placeholder="50.00"
								:background="'#ffffff'"
								:radius="'8'"
								:border="true"
								borderColor="#e5e7eb"
							></fui-input>
							<text class="unit">/小时</text>
						</view>
					</view>
					
					<view class="modal-buttons">
						<fui-button 
							:width="'48%'" 
							:height="'40'" 
							:size="'14'" 
							background="#f3f4f6"
							color="#374151"
							:radius="'8'"
							@click="hideEditModal"
						>
							取消
						</fui-button>
						<fui-button 
							:width="'48%'" 
							:height="'40'" 
							:size="'14'" 
							background="#3b82f6"
							color="#ffffff"
							:radius="'8'"
							@click="confirmEdit"
						>
							保存
						</fui-button>
					</view>
				</view>
			</view>
		</fui-modal>

		<!-- 底部导航 -->
		<tabbar></tabbar>
	</view>
</template>

<script setup>
	import { ref, reactive } from 'vue'
	import { useStoragePiniaStore } from "@/pinia/storage.js"
	import tabbar from "@/components/common/l-tabbar.vue"
	
	const useStorage = useStoragePiniaStore()
	
	// 隐藏系统tabbar
	uni.hideTabBar()
	
	// 响应式数据
	const showEditUserModal = ref(false)
	
	const userInfo = ref({
		nickname: '小小攀',
		avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face&auto=format&q=80',
		phone: '138****8000',
		registerDays: 365,
		hourlyRate: '50.00'
	})
	
	const userStats = ref([
		{
			id: 1,
			value: '365',
			label: '总工作天数',
			color: '#3b82f6'
		},
		{
			id: 2,
			value: '2,920h',
			label: '累计工时',
			color: '#10b981'
		},
		{
			id: 3,
			value: '¥146K',
			label: '总收入',
			color: '#f59e0b'
		}
	])
	
	const functionSettings = ref([
		{
			id: 1,
			title: '时间段管理',
			description: '自定义工作时间段',
			icon: 'clock',
			iconColor: '#3b82f6',
			iconBg: '#dbeafe',
			badge: '3个'
		},
		{
			id: 2,
			title: '计件类型管理',
			description: '管理计件工作类型',
			icon: 'tags',
			iconColor: '#8b5cf6',
			iconBg: '#ede9fe',
			badge: '5个'
		},
		{
			id: 3,
			title: '数据导出',
			description: '导出Excel报表',
			icon: 'download',
			iconColor: '#f59e0b',
			iconBg: '#fef3c7'
		},
		{
			id: 4,
			title: '数据备份',
			description: '云端安全备份',
			icon: 'cloud-upload-alt',
			iconColor: '#6366f1',
			iconBg: '#e0e7ff',
			badge: '已同步'
		}
	])
	
	const appSettings = ref([
		{
			id: 1,
			title: '消息提醒',
			description: '记录提醒和统计推送',
			icon: 'bell',
			iconColor: '#ef4444',
			iconBg: '#fee2e2',
			type: 'switch',
			value: true
		},
		{
			id: 2,
			title: '深色模式',
			description: '护眼深色主题',
			icon: 'moon',
			iconColor: '#f59e0b',
			iconBg: '#fef3c7',
			type: 'switch',
			value: false
		},
		{
			id: 3,
			title: '隐私设置',
			description: '数据隐私保护',
			icon: 'shield-alt',
			iconColor: '#14b8a6',
			iconBg: '#ccfbf1'
		}
	])
	
	const moreSettings = ref([
		{
			id: 1,
			title: '意见反馈',
			description: '帮助我们改进',
			icon: 'comment-dots',
			iconColor: '#ec4899',
			iconBg: '#fce7f3'
		},
		{
			id: 2,
			title: '帮助中心',
			description: '使用指南和FAQ',
			icon: 'question-circle',
			iconColor: '#3b82f6',
			iconBg: '#dbeafe'
		},
		{
			id: 3,
			title: '关于我们',
			description: '版本信息 v1.2.0',
			icon: 'info-circle',
			iconColor: '#6b7280',
			iconBg: '#f3f4f6'
		}
	])
	
	const editForm = reactive({
		nickname: '',
		avatar: '',
		phone: '',
		hourlyRate: ''
	})
	
	// 方法
	const showEditModal = () => {
		// 复制当前用户信息到编辑表单
		editForm.nickname = userInfo.value.nickname
		editForm.avatar = userInfo.value.avatar
		editForm.phone = userInfo.value.phone
		editForm.hourlyRate = userInfo.value.hourlyRate
		
		showEditUserModal.value = true
	}
	
	const hideEditModal = () => {
		showEditUserModal.value = false
	}
	
	const changeAvatar = () => {
		uni.chooseImage({
			count: 1,
			sizeType: ['compressed'],
			sourceType: ['album', 'camera'],
			success: (res) => {
				editForm.avatar = res.tempFilePaths[0]
			}
		})
	}
	
	const confirmEdit = () => {
		// 更新用户信息
		userInfo.value.nickname = editForm.nickname
		userInfo.value.avatar = editForm.avatar
		userInfo.value.phone = editForm.phone
		userInfo.value.hourlyRate = editForm.hourlyRate
		
		// 保存到本地存储
		useStorage.savePiniaData('pinia_user.nickname', editForm.nickname)
		useStorage.savePiniaData('pinia_user.avatar', editForm.avatar)
		useStorage.savePiniaData('pinia_user.phone', editForm.phone)
		useStorage.savePiniaData('pinia_user.hourlyRate', editForm.hourlyRate)
		
		hideEditModal()
		
		uni.showToast({
			title: '保存成功',
			icon: 'success'
		})
	}
	
	const handleSettingClick = (item) => {
		if (item.id === 3) { // 数据导出
			uni.showActionSheet({
				itemList: ['导出Excel', '导出PDF'],
				success: (res) => {
					const actions = ['Excel导出中...', 'PDF生成中...']
					uni.showLoading({
						title: actions[res.tapIndex]
					})
					
					setTimeout(() => {
						uni.hideLoading()
						uni.showToast({
							title: '导出成功',
							icon: 'success'
						})
					}, 2000)
				}
			})
		} else {
			uni.showToast({
				title: item.title,
				icon: 'none'
			})
		}
	}
	
	const handleAppSettingClick = (item) => {
		if (item.type !== 'switch') {
			uni.showToast({
				title: item.title,
				icon: 'none'
			})
		}
	}
	
	const handleSwitchChange = (item) => {
		uni.showToast({
			title: `${item.title}已${item.value ? '开启' : '关闭'}`,
			icon: 'none'
		})
	}
	
	const handleMoreClick = (item) => {
		if (item.id === 1) { // 意见反馈
			uni.showModal({
				title: '意见反馈',
				content: '感谢您的反馈，我们会持续改进产品体验',
				showCancel: false
			})
		} else if (item.id === 2) { // 帮助中心
			uni.showModal({
				title: '帮助中心',
				content: '如有问题请联系客服或查看使用指南',
				showCancel: false
			})
		} else if (item.id === 3) { // 关于我们
			uni.showModal({
				title: '关于我们',
				content: '蓉蓉の小账本 v1.2.0\n专业的工时计件记录工具',
				showCancel: false
			})
		}
	}
	
	const handleLogout = () => {
		uni.showModal({
			title: '确认退出',
			content: '确定要退出登录吗？',
			success: (res) => {
				if (res.confirm) {
					// 清除用户信息
					useStorage.savePiniaData('pinia_user', {})
					
					// 跳转到登录页
					uni.reLaunch({
						url: '/pages/login/index'
					})
				}
			}
		})
	}
</script>

<style lang="scss" scoped>
	.profile-container {
		min-height: 100vh;
		background: linear-gradient(to bottom right, #e0f2fe, #f0f9ff);
		padding-bottom: 160rpx;
	}
	
	.profile-header {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 0 0 48rpx 48rpx;
		padding: 32rpx 48rpx 64rpx;
		color: #ffffff;
		box-shadow: 0 16rpx 60rpx rgba(102, 126, 234, 0.3);
		
		.header-top {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 48rpx;
			
			.header-title {
				font-size: 42rpx;
				font-weight: bold;
			}
			
			.edit-btn {
				background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
				border-radius: 24rpx;
				padding: 16rpx 32rpx;
				display: flex;
				align-items: center;
				box-shadow: 0 8rpx 30rpx rgba(59, 130, 246, 0.3);
				
				.edit-text {
					color: #ffffff;
					font-size: 28rpx;
					font-weight: 500;
					margin-left: 8rpx;
				}
			}
		}
		
		.user-info {
			display: flex;
			align-items: center;
			
			.avatar-container {
				background: rgba(255, 255, 255, 0.2);
				border-radius: 50%;
				padding: 6rpx;
				margin-right: 32rpx;
				
				.user-avatar {
					width: 160rpx;
					height: 160rpx;
					border-radius: 50%;
				}
			}
			
			.user-details {
				flex: 1;
				
				.user-name {
					font-size: 48rpx;
					font-weight: bold;
					margin-bottom: 8rpx;
				}
				
				.user-desc {
					font-size: 28rpx;
					opacity: 0.8;
					margin-bottom: 16rpx;
				}
				
				.user-meta {
					display: flex;
					gap: 32rpx;
					font-size: 28rpx;
					opacity: 0.9;
					
					.meta-item {
						display: flex;
						align-items: center;
						
						.meta-text {
							margin-left: 8rpx;
						}
					}
				}
			}
		}
	}
	
	.stats-overview {
		padding: 32rpx 48rpx;
		margin-top: -32rpx;
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 24rpx;
		
		.stat-card {
			background: rgba(255, 255, 255, 0.95);
			backdrop-filter: blur(20rpx);
			border: 2rpx solid rgba(255, 255, 255, 0.2);
			border-radius: 32rpx;
			padding: 32rpx;
			text-align: center;
			
			.stat-value {
				font-size: 48rpx;
				font-weight: bold;
				margin-bottom: 8rpx;
			}
			
			.stat-label {
				font-size: 24rpx;
				color: #6b7280;
			}
		}
	}
	
	.hourly-rate-section {
		padding: 16rpx 48rpx 32rpx;
		
		.rate-card {
			background: rgba(255, 255, 255, 0.95);
			backdrop-filter: blur(20rpx);
			border: 2rpx solid rgba(255, 255, 255, 0.2);
			border-radius: 32rpx;
			padding: 32rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			
			.rate-left {
				display: flex;
				align-items: center;
				
				.rate-icon {
					width: 80rpx;
					height: 80rpx;
					background: #d1fae5;
					border-radius: 24rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					margin-right: 24rpx;
				}
				
				.rate-info {
					.rate-title {
						font-size: 32rpx;
						font-weight: 600;
						color: #1f2937;
						margin-bottom: 8rpx;
					}
					
					.rate-desc {
						font-size: 28rpx;
						color: #6b7280;
					}
				}
			}
			
			.rate-right {
				text-align: right;
				
				.rate-amount {
					font-size: 42rpx;
					font-weight: bold;
					color: #10b981;
					margin-bottom: 4rpx;
				}
				
				.rate-unit {
					font-size: 24rpx;
					color: #9ca3af;
				}
			}
		}
	}
	
	.function-settings,
	.app-settings,
	.more-functions {
		padding: 16rpx 48rpx 32rpx;
		
		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 24rpx;
			
			.title-text {
				font-size: 36rpx;
				font-weight: 600;
				color: #1f2937;
				margin-left: 16rpx;
			}
		}
		
		.settings-list {
			.setting-item {
				background: rgba(255, 255, 255, 0.9);
				border: 2rpx solid rgba(229, 231, 235, 0.5);
				border-radius: 24rpx;
				padding: 32rpx;
				margin-bottom: 24rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				
				.setting-left {
					display: flex;
					align-items: center;
					flex: 1;
					
					.setting-icon {
						width: 80rpx;
						height: 80rpx;
						border-radius: 24rpx;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 24rpx;
					}
					
					.setting-info {
						.setting-title {
							font-size: 32rpx;
							font-weight: 500;
							color: #1f2937;
							margin-bottom: 8rpx;
						}
						
						.setting-desc {
							font-size: 28rpx;
							color: #6b7280;
						}
					}
				}
				
				.setting-right {
					display: flex;
					align-items: center;
					gap: 16rpx;
					
					.setting-badge {
						background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
						color: #ffffff;
						padding: 8rpx 16rpx;
						border-radius: 24rpx;
						font-size: 24rpx;
						font-weight: 500;
						box-shadow: 0 8rpx 30rpx rgba(245, 158, 11, 0.3);
					}
				}
			}
		}
	}
	
	.logout-section {
		padding: 32rpx 48rpx;
		
		.logout-btn-content {
			display: flex;
			align-items: center;
			justify-content: center;
			
			.logout-text {
				margin-left: 16rpx;
			}
		}
	}
	
	.edit-modal {
		padding: 48rpx;
		
		.modal-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 32rpx;
			
			.modal-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #1f2937;
			}
			
			.close-btn {
				width: 48rpx;
				height: 48rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
		
		.modal-content {
			.avatar-edit {
				text-align: center;
				margin-bottom: 32rpx;
				
				.avatar-preview {
					position: relative;
					display: inline-block;
					
					.preview-image {
						width: 160rpx;
						height: 160rpx;
						border-radius: 50%;
					}
					
					.camera-btn {
						position: absolute;
						bottom: 0;
						right: 0;
						width: 48rpx;
						height: 48rpx;
						background: #3b82f6;
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
					}
				}
			}
			
			.input-group {
				margin-bottom: 32rpx;
				
				.input-label {
					display: block;
					font-size: 28rpx;
					font-weight: 500;
					color: #374151;
					margin-bottom: 16rpx;
				}
				
				.hourly-input {
					display: flex;
					align-items: center;
					gap: 16rpx;
					
					.currency,
					.unit {
						font-size: 32rpx;
						color: #6b7280;
					}
				}
			}
			
			.modal-buttons {
				display: flex;
				justify-content: space-between;
				margin-top: 48rpx;
			}
		}
	}
	
	.bottom-safe {
		height: 40rpx;
	}
</style>
