<template>
	<view class="lui-input">
		<view class="label" v-if="label || $slots.labelLeft || $slots.labelRight">
			<view class="left">
				<template v-if="label">
					<text class="name">{{label}}</text>
					<text class="required" v-if="required">（*必填）</text>
					<text class="no-required" v-if="noRequired">（选填）</text>
				</template>
				<slot name="labelLeft" v-else></slot>
			</view>
			<view class="right" v-if="$slots.labelRight">
				<slot name="labelRight"></slot>
			</view>
		</view>
		<view class="warp" @click="handleInputClick">
			<view class="left">
				<slot name="left"></slot>
			</view>
			<input :value="_value" :maxlength="maxlength" class="input" :placeholder="placeholder" :cursor-spacing="176"
				:disabled="_disabled" @input="onInupt" />
			<view class="right">
				<slot name="right"></slot>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		watch
	} from "vue";
	const emits = defineEmits(['update:value', "click"])
	const props = defineProps({
		value: {
			type: String,
			default: ""
		},
		disabled: {
			type: Boolean,
			default: false
		},
		label: {
			type: String,
			default: ""
		},
		required: {
			type: Boolean,
			default: false
		},
		noRequired: {
			type: Boolean,
			default: false
		},
		placeholder: {
			type: String,
			default: ""
		},
		maxlength: {
			type: String,
			default: "500"
		},

	});


	const _value = ref(props.value);
	const _disabled = ref(props.disabled);
	watch(() => props.disabled, (newVal) => {
		_disabled.value = newVal;
	})
	watch(() => props.value, (val) => {
		_value.value = val;
	})

	const onInupt = (e) => {
		emits("update:value", e.detail.value)
	}

	const handleInputClick = () => {
		emits("click")
	}
</script>

<style lang="less" scoped>
	.lui-input {
		.label {
			margin-bottom: 40rpx;
			display: flex;
			justify-content: space-between;
			align-items: center;

			.left {
				font-size: 32rpx;
				color: #030303;

				.required {
					font-size: 24rpx;
					color: #FF2C2C;
				}

				.no-required {
					font-size: 24rpx;
					color: #999;
				}
			}

			.right {}
		}

		.warp {
			height: 88rpx;
			background-color: #F2F4F5;
			border-radius: 8rpx;
			display: flex;
			align-items: center;

			.left {
				flex-shrink: 0;
			}

			.right {
				flex-shrink: 0;
			}

			.input {
				font-size: 28rpx;
				padding: 0 24rpx;
				height: 88rpx;
				width: 100%;
				overflow: hidden;
				white-space: nowrap;
				text-overflow: ellipsis;
			}
		}
	}
</style>