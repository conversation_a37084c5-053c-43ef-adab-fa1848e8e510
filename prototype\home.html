<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=375, initial-scale=1.0, user-scalable=no">
    <title>蓉蓉の记账本 - 主页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 自定义样式 */
        body {
            width: 375px;
            margin: 0 auto;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #E8F5E9 0%, #F8F8F8 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }

        /* 数字字体 */
        .amount-text {
            font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
            font-weight: 600;
        }

        /* 毛玻璃效果 */
        .glass-card {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
        }

        .glass-card:hover {
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
            transform: translateY(-2px);
        }

        /* 收入卡片新配色方案 - 清新精致的蓝白渐变 */
        .income-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 16px 48px rgba(102, 126, 234, 0.25);
        }

        .income-card:hover {
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.35);
            transform: translateY(-2px);
        }

        /* 固定顶部导航栏样式 */
        .fixed-navbar {
            position: fixed;
            top: 44px; /* 状态栏高度 */
            left: 0;
            right: 0;
            z-index: 100;
            background: rgba(255, 255, 255, 0.85);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 2px 20px rgba(0, 0, 0, 0.05);
        }

        /* 主内容区域顶部间距 */
        .main-content {
            padding-top: 120px; /* 状态栏 + 导航栏高度 */
        }

        /* 快速操作按钮 */
        .quick-btn {
            background: rgba(255, 255, 255, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.2s ease;
        }

        .quick-btn:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: scale(0.98);
        }

        /* 图标容器 */
        .icon-container {
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .icon-blue { background: #DBEAFE; }
        .icon-green { background: #DCFCE7; }
        .icon-white { background: rgba(255, 255, 255, 0.9); }

        /* 标签样式 */
        .tag-work {
            background: rgba(37, 99, 235, 0.1);
            color: #2563EB;
        }

        .tag-piece {
            background: rgba(22, 163, 74, 0.1);
            color: #16A34A;
        }

        /* 悬浮按钮 */
        .fab-button {
            background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
            box-shadow: 0 8px 24px rgba(76, 175, 80, 0.3);
            transition: all 0.2s ease;
        }

        .fab-button:hover {
            box-shadow: 0 12px 32px rgba(76, 175, 80, 0.4);
            transform: scale(0.95);
        }

        /* 底部导航 */
        .bottom-nav {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-item.active {
            color: #4CAF50;
        }

        /* 动画效果 */
        .fade-in {
            animation: fadeIn 0.3s ease-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 状态栏 */
        .status-bar {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
        }

        /* 增长指示器 */
        .growth-up { color: #EF4444; }
        .growth-down { color: #10B981; }
    </style>
</head>
<body>
    <!-- 状态栏 -->
    <div class="status-bar h-11 flex items-center justify-between px-4 text-sm">
        <div class="text-gray-600">9:41</div>
        <div class="flex items-center gap-1">
            <div class="w-4 h-2 bg-gray-600 rounded-sm"></div>
            <div class="text-gray-600">100%</div>
        </div>
    </div>

    <!-- 固定顶部导航栏 -->
    <div class="fixed-navbar">
        <div class="flex items-center justify-between py-4 px-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-800">记账本</h1>
                <p class="text-sm text-gray-600">今天是美好的一天</p>
            </div>
            <div class="flex items-center gap-2 bg-blue-100 px-3 py-1 rounded-full">
                <img src="https://unpkg.com/lucide-static@latest/icons/calendar.svg"
                     alt="日历" class="w-4 h-4" style="filter: invert(27%) sepia(98%) saturate(1352%) hue-rotate(214deg) brightness(96%) contrast(94%);">
                <span class="text-sm font-medium text-blue-600">今天</span>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content px-4 pb-20">

        <!-- 今日收入概览 -->
        <div class="income-card rounded-3xl p-6 text-white mb-6 fade-in">
            <div class="mb-4">
                <h2 class="text-lg font-semibold mb-4 text-white">今日收入概览</h2>

                <!-- 收入详情 -->
                <div class="space-y-3 mb-4">
                    <div class="flex justify-between items-center">
                        <span class="text-white/90">工时收入</span>
                        <span class="text-xl font-bold amount-text text-white">¥128.00</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-white/90">计件收入</span>
                        <span class="text-xl font-bold amount-text text-white">¥85.50</span>
                    </div>
                </div>

                <!-- 分割线 -->
                <div class="h-px bg-white/40 my-4"></div>

                <!-- 总收入 -->
                <div class="flex justify-between items-center mb-2">
                    <span class="text-lg font-semibold text-white">总收入</span>
                    <span class="text-3xl font-bold amount-text text-white">¥213.50</span>
                </div>

                <!-- 增长指示器 -->
                <div class="flex justify-between items-center">
                    <span class="text-white/90 text-sm">比昨天</span>
                    <div class="flex items-center gap-1">
                        <img src="https://unpkg.com/lucide-static@latest/icons/trending-up.svg"
                             alt="增长" class="w-4 h-4" style="filter: invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);">
                        <span class="text-sm font-medium text-white">增长 12.5%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速记录 -->
        <div class="mb-6 fade-in">
            <div class="flex items-center mb-4">
                <img src="https://unpkg.com/lucide-static@latest/icons/zap.svg" 
                     alt="快速" class="w-5 h-5 mr-2" style="filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(87deg) brightness(119%) contrast(119%);">
                <h3 class="text-lg font-semibold text-gray-800">快速记录</h3>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
                <!-- 工时记录 -->
                <button class="quick-btn rounded-2xl p-4 text-center" onclick="goToWorkHours()">
                    <div class="icon-container icon-blue w-12 h-12 mx-auto mb-3">
                        <img src="https://unpkg.com/lucide-static@latest/icons/clock.svg" 
                             alt="工时" class="w-6 h-6" style="filter: invert(27%) sepia(98%) saturate(1352%) hue-rotate(214deg) brightness(96%) contrast(94%);">
                    </div>
                    <div class="font-semibold text-gray-800 mb-1">工时记录</div>
                    <div class="text-sm text-gray-600">时间段记录</div>
                </button>

                <!-- 计件记录 -->
                <button class="quick-btn rounded-2xl p-4 text-center" onclick="goToPieceWork()">
                    <div class="icon-container icon-green w-12 h-12 mx-auto mb-3">
                        <img src="https://unpkg.com/lucide-static@latest/icons/package.svg" 
                             alt="计件" class="w-6 h-6" style="filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(87deg) brightness(119%) contrast(119%);">
                    </div>
                    <div class="font-semibold text-gray-800 mb-1">计件记录</div>
                    <div class="text-sm text-gray-600">数量统计</div>
                </button>
            </div>
        </div>

        <!-- 今日记录 -->
        <div class="fade-in">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center">
                    <img src="https://unpkg.com/lucide-static@latest/icons/list.svg" 
                         alt="列表" class="w-5 h-5 mr-2" style="filter: invert(27%) sepia(98%) saturate(1352%) hue-rotate(214deg) brightness(96%) contrast(94%);">
                    <h3 class="text-lg font-semibold text-gray-800">今日记录</h3>
                </div>
                <span class="text-sm text-gray-600">(3条)</span>
            </div>

            <!-- 记录列表 -->
            <div class="space-y-3">
                <!-- 工时记录1 -->
                <div class="glass-card rounded-2xl p-4 relative">
                    <div class="absolute top-0 right-0 tag-work px-2 py-1 rounded-bl-lg rounded-tr-2xl text-xs font-semibold">
                        工时
                    </div>
                    <div class="flex items-center">
                        <div class="icon-container icon-blue w-8 h-8 mr-3">
                            <img src="https://unpkg.com/lucide-static@latest/icons/clock.svg" 
                                 alt="工时" class="w-4 h-4" style="filter: invert(27%) sepia(98%) saturate(1352%) hue-rotate(214deg) brightness(96%) contrast(94%);">
                        </div>
                        <div class="flex-1">
                            <div class="font-semibold text-gray-800">09:00-12:00 工时</div>
                            <div class="text-sm text-gray-600">3小时 · 数据录入</div>
                        </div>
                        <div class="text-lg font-bold text-green-600 amount-text">¥90.00</div>
                    </div>
                </div>

                <!-- 工时记录2 -->
                <div class="glass-card rounded-2xl p-4 relative">
                    <div class="absolute top-0 right-0 tag-work px-2 py-1 rounded-bl-lg rounded-tr-2xl text-xs font-semibold">
                        工时
                    </div>
                    <div class="flex items-center">
                        <div class="icon-container icon-blue w-8 h-8 mr-3">
                            <img src="https://unpkg.com/lucide-static@latest/icons/clock.svg" 
                                 alt="工时" class="w-4 h-4" style="filter: invert(27%) sepia(98%) saturate(1352%) hue-rotate(214deg) brightness(96%) contrast(94%);">
                        </div>
                        <div class="flex-1">
                            <div class="font-semibold text-gray-800">14:00-17:00 工时</div>
                            <div class="text-sm text-gray-600">3小时 · 文档整理</div>
                        </div>
                        <div class="text-lg font-bold text-green-600 amount-text">¥90.00</div>
                    </div>
                </div>

                <!-- 计件记录 -->
                <div class="glass-card rounded-2xl p-4 relative">
                    <div class="absolute top-0 right-0 tag-piece px-2 py-1 rounded-bl-lg rounded-tr-2xl text-xs font-semibold">
                        计件
                    </div>
                    <div class="flex items-center">
                        <div class="icon-container icon-green w-8 h-8 mr-3">
                            <img src="https://unpkg.com/lucide-static@latest/icons/package.svg" 
                                 alt="计件" class="w-4 h-4" style="filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(87deg) brightness(119%) contrast(119%);">
                        </div>
                        <div class="flex-1">
                            <div class="font-semibold text-gray-800">计件×17个</div>
                            <div class="text-sm text-gray-600">包装盒 · ¥5.00/个</div>
                        </div>
                        <div class="text-lg font-bold text-green-600 amount-text">¥85.00</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 悬浮添加按钮 -->
    <button class="fab-button fixed bottom-24 right-4 w-14 h-14 rounded-full flex items-center justify-center" onclick="showAddOptions()">
        <img src="https://unpkg.com/lucide-static@latest/icons/plus.svg" 
             alt="添加" class="w-6 h-6" style="filter: invert(1);">
    </button>

    <!-- 底部导航 -->
    <div class="bottom-nav fixed bottom-0 left-0 right-0 h-20 flex items-center justify-around">
        <div class="nav-item active flex flex-col items-center">
            <img src="https://unpkg.com/lucide-static@latest/icons/home.svg" 
                 alt="首页" class="w-6 h-6 mb-1" style="filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(87deg) brightness(119%) contrast(119%);">
            <span class="text-xs font-medium">记录</span>
        </div>
        <div class="nav-item flex flex-col items-center text-gray-500">
            <img src="https://unpkg.com/lucide-static@latest/icons/bar-chart-3.svg" 
                 alt="统计" class="w-6 h-6 mb-1">
            <span class="text-xs">统计</span>
        </div>
        <div class="nav-item flex flex-col items-center text-gray-500">
            <img src="https://unpkg.com/lucide-static@latest/icons/user.svg" 
                 alt="我的" class="w-6 h-6 mb-1">
            <span class="text-xs">我的</span>
        </div>
    </div>

    <script>
        // 页面交互功能
        function goToWorkHours() {
            alert('跳转到工时记录页面');
        }

        function goToPieceWork() {
            alert('跳转到计件记录页面');
        }

        function showAddOptions() {
            const options = ['工时记录', '计件记录'];
            const choice = prompt('选择记录类型:\n1. 工时记录\n2. 计件记录\n\n请输入数字 1 或 2:');
            
            if (choice === '1') {
                goToWorkHours();
            } else if (choice === '2') {
                goToPieceWork();
            }
        }

        // 模拟数据更新
        function updateIncomeData() {
            const totalElement = document.querySelector('.amount-text');
            if (totalElement) {
                const currentValue = parseFloat(totalElement.textContent.replace('¥', ''));
                const newValue = currentValue + (Math.random() * 10 - 5);
                if (newValue > 0) {
                    totalElement.textContent = `¥${newValue.toFixed(2)}`;
                }
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 添加淡入动画
            const elements = document.querySelectorAll('.fade-in');
            elements.forEach((el, index) => {
                el.style.animationDelay = `${index * 0.1}s`;
            });

            // 模拟数据更新（每10秒）
            setInterval(updateIncomeData, 10000);
        });
    </script>
</body>
</html>
