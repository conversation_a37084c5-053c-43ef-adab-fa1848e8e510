<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=375, initial-scale=1.0, user-scalable=no">
    <title>蓉蓉の记账本 - 登录</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 自定义样式 */
        body {
            width: 375px;
            margin: 0 auto;
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%);
            min-height: 100vh;
            overflow-x: hidden;
        }
        
        .app-logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
            margin-bottom: 20px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.15);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 16px;
            padding: 12px 8px;
            text-align: center;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-4px);
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 36px;
            height: 36px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 10px;
            background: rgba(255, 255, 255, 0.9);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }

        .feature-title {
            font-size: 13px;
            font-weight: 600;
            color: #ffffff;
            line-height: 1.2;
        }
        
        .wechat-btn {
            background: #07C160;
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 24px;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 12px;
            box-shadow: 0 4px 16px rgba(7, 193, 96, 0.3);
            transition: all 0.2s ease;
        }
        
        .wechat-btn:hover {
            background: #06AD56;
            transform: translateY(-1px);
            box-shadow: 0 6px 20px rgba(7, 193, 96, 0.4);
        }
        
        .floating-elements {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
        }
        
        .floating-circle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-circle:nth-child(1) {
            width: 60px;
            height: 60px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .floating-circle:nth-child(2) {
            width: 40px;
            height: 40px;
            top: 20%;
            right: 15%;
            animation-delay: 2s;
        }
        
        .floating-circle:nth-child(3) {
            width: 80px;
            height: 80px;
            bottom: 20%;
            left: 5%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        .amount-text {
            color: #4CAF50;
            font-weight: 600;
        }
        


        .checkbox-container {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-top: 20px;
            margin-bottom: 16px;
        }

        .custom-checkbox {
            width: 18px;
            height: 18px;
            border: 2px solid rgba(255, 255, 255, 0.6);
            border-radius: 4px;
            background: transparent;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .custom-checkbox.checked {
            background: #ffffff;
            border-color: #ffffff;
        }

        .custom-checkbox .checkmark {
            width: 10px;
            height: 10px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .custom-checkbox.checked .checkmark {
            opacity: 1;
        }

        .wechat-btn:disabled {
            background: rgba(255, 255, 255, 0.3);
            color: rgba(255, 255, 255, 0.6);
            cursor: not-allowed;
            box-shadow: none;
            transform: none;
        }
    </style>
</head>
<body>
    <!-- 浮动装饰元素 -->
    <div class="floating-elements">
        <div class="floating-circle"></div>
        <div class="floating-circle"></div>
        <div class="floating-circle"></div>
    </div>

    <div class="relative min-h-screen flex flex-col">
        <!-- 顶部状态栏占位 -->
        <div class="h-11"></div>
        
        <!-- 主要内容区域 -->
        <div class="px-6 py-8">
            <!-- 应用Logo和标题 -->
            <div class="text-center mb-12">
                <div class="app-logo mx-auto mb-6">
                    <img src="https://unpkg.com/lucide-static@latest/icons/book-open.svg"
                         alt="记账本" class="w-16 h-16" style="filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(87deg) brightness(119%) contrast(119%);">
                </div>
                <h1 class="text-3xl font-bold text-white mb-2">蓉蓉の记账本</h1>
                <p class="text-white/80 text-base">让每一份收入都清晰可见</p>
            </div>
            
            <!-- 功能特色展示 -->
            <div class="features-grid">
                <!-- 工时记录 -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <img src="https://unpkg.com/lucide-static@latest/icons/clock.svg"
                             alt="工时记录" class="w-5 h-5" style="filter: invert(27%) sepia(98%) saturate(1352%) hue-rotate(214deg) brightness(96%) contrast(94%);">
                    </div>
                    <div class="feature-title">工时记录</div>
                </div>

                <!-- 计件记录 -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <img src="https://unpkg.com/lucide-static@latest/icons/package.svg"
                             alt="计件记录" class="w-5 h-5" style="filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(87deg) brightness(119%) contrast(119%);">
                    </div>
                    <div class="feature-title">计件记录</div>
                </div>

                <!-- 数据统计 -->
                <div class="feature-card">
                    <div class="feature-icon">
                        <img src="https://unpkg.com/lucide-static@latest/icons/trending-up.svg"
                             alt="数据统计" class="w-5 h-5" style="filter: invert(69%) sepia(58%) saturate(2618%) hue-rotate(316deg) brightness(101%) contrast(101%);">
                    </div>
                    <div class="feature-title">数据统计</div>
                </div>
            </div>
        </div>
        
        <!-- 底部登录区域 -->
        <div class="px-6 pb-8">
            <button class="wechat-btn" id="loginBtn" onclick="handleWechatLogin()" disabled>
                <img src="https://unpkg.com/lucide-static@latest/icons/message-circle.svg"
                     alt="微信" class="w-6 h-6" style="filter: invert(1);">
                微信一键登录
            </button>

            <!-- 用户协议勾选 -->
            <div class="checkbox-container">
                <div class="custom-checkbox" id="agreementCheckbox" onclick="toggleAgreement()">
                    <img src="https://unpkg.com/lucide-static@latest/icons/check.svg"
                         alt="勾选" class="checkmark" style="filter: invert(42%) sepia(93%) saturate(1352%) hue-rotate(87deg) brightness(119%) contrast(119%);">
                </div>
                <p class="text-white/80 text-xs">
                    我已阅读并同意
                    <a href="#" class="text-white underline">《用户协议》</a>
                    和
                    <a href="#" class="text-white underline">《隐私政策》</a>
                </p>
            </div>
        </div>
        
        <!-- 底部安全区域 -->
        <div class="h-8"></div>
    </div>

    <script>
        let isAgreementChecked = false;

        function toggleAgreement() {
            const checkbox = document.getElementById('agreementCheckbox');
            const loginBtn = document.getElementById('loginBtn');

            isAgreementChecked = !isAgreementChecked;

            if (isAgreementChecked) {
                checkbox.classList.add('checked');
                loginBtn.disabled = false;
            } else {
                checkbox.classList.remove('checked');
                loginBtn.disabled = true;
            }
        }

        function handleWechatLogin() {
            if (!isAgreementChecked) {
                alert('请先同意用户协议和隐私政策');
                return;
            }

            // 模拟微信登录流程
            const btn = document.getElementById('loginBtn');
            const originalText = btn.innerHTML;

            btn.innerHTML = `
                <img src="https://unpkg.com/lucide-static@latest/icons/loader-2.svg"
                     alt="加载中" class="w-6 h-6 animate-spin" style="filter: invert(1);">
                登录中...
            `;
            btn.disabled = true;

            setTimeout(() => {
                alert('登录成功！即将跳转到首页...');
                btn.innerHTML = originalText;
                btn.disabled = !isAgreementChecked;
                // 这里可以跳转到首页
                // window.location.href = 'home.html';
            }, 2000);
        }
    </script>
</body>
</html>
