<template>
	<view class="fixed-bottom" :style="[customStyle]">
		<slot></slot>
		<BottomSafe></BottomSafe>
	</view>
</template>

<script setup>
	import BottomSafe from "../lui-bottomsafe/lui-bottomsafe.vue";
	defineProps({
		customStyle: {
			type: Object,
			default: () => ({})
		}
	})
</script>

<style lang="scss" scoped>
	.fixed-bottom {
		padding: 0 30rpx;
		position: fixed;
		bottom: 0;
		left: 0;
		width: 100%;
		z-index: 99;
	}
</style>