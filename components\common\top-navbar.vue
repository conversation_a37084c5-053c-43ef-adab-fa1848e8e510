<template>
	<view class="top-navbar" 
		:class="{ 'top-navbar--fixed': fixed }"
		:style="navbarStyle">
		
		<!-- 状态栏占位 -->
		<fui-status-bar v-if="statusBar"></fui-status-bar>
		
		<!-- 导航栏内容 -->
		<view class="navbar-content" 
			:style="{ 
				height: (statusBarHeight + navBarHeight) + 'px' 
			}">
			
			<!-- 主标题行 -->
			<view class="navbar-header" :style="{ height: navBarHeight + 'px' }">
				<view class="navbar-left">
					<view class="navbar-title" @click="handleTitleClick">{{ title }}</view>
				</view>
			</view>

			<!-- 副标题行 -->
			<view class="navbar-subtitle-row" v-if="subtitle || badge">
				<view class="navbar-subtitle" v-if="subtitle">{{ subtitle }}</view>

				<!-- 右侧徽章区域 -->
				<view class="navbar-right" 
					v-if="badge"
					:style="{ height: (menuButtonInfo.height || 32) + 'px' }"
					@click="handleBadgeClick">
					<view class="navbar-badge" :class="badgeClass" :style="badgeStyle">
						<fui-icon 
							v-if="badge.icon"
							custom-prefix="iconfont" 
							:name="badge.icon" 
							:size="badge.iconSize || 28" 
							:color="badgeIconColor">
						</fui-icon>
						<text class="badge-text">{{ badge.text }}</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
import { computed } from 'vue'
import { useSystemInfo } from '@/hooks/useSystemInfo.js'

// Props 定义
const props = defineProps({
	// 基础内容
	title: {
		type: String,
		required: true
	},
	subtitle: {
		type: String,
		default: ''
	},
	
	// 右侧徽章配置
	badge: {
		type: Object,
		default: null
		// 格式: { text: string, icon: string, theme: 'blue'|'green'|'custom', background?: string, color?: string, iconSize?: number }
	},
	
	// 样式配置
	background: {
		type: String,
		default: 'rgba(255, 255, 255, 0.9)'
	},
	blur: {
		type: Boolean,
		default: true
	},
	shadow: {
		type: Boolean,
		default: true
	},
	
	// 系统适配
	statusBar: {
		type: Boolean,
		default: true
	},
	fixed: {
		type: Boolean,
		default: true
	},
	zIndex: {
		type: [Number, String],
		default: 999
	}
})

// 事件定义
const emit = defineEmits(['badge-click', 'title-click'])

// 使用系统信息 hook
const { systemInfo, menuButtonInfo, navBarHeight, statusBarHeight, getSystemInfo } = useSystemInfo()

// 初始化系统信息
getSystemInfo()

// 计算导航栏样式
const navbarStyle = computed(() => {
	const styles = {
		background: props.background,
		zIndex: props.zIndex
	}
	
	if (props.blur) {
		styles.backdropFilter = 'blur(20rpx)'
	}
	
	if (props.shadow) {
		styles.boxShadow = '0 2rpx 16rpx rgba(0, 0, 0, 0.1)'
	}
	
	return styles
})

// 主题预设
const themePresets = {
	blue: {
		background: '#EFF6FF',
		color: '#2563EB'
	},
	green: {
		background: '#D1FAE5',
		color: '#10b981'
	}
}

// 计算徽章样式类
const badgeClass = computed(() => {
	if (!props.badge) return ''
	return `navbar-badge--${props.badge.theme || 'blue'}`
})

// 计算徽章样式
const badgeStyle = computed(() => {
	if (!props.badge) return {}
	
	const theme = props.badge.theme || 'blue'
	const preset = themePresets[theme]
	
	if (theme === 'custom') {
		return {
			background: props.badge.background || '#EFF6FF',
			color: props.badge.color || '#2563EB'
		}
	}
	
	return preset ? {
		background: preset.background,
		color: preset.color
	} : {}
})

// 计算徽章图标颜色
const badgeIconColor = computed(() => {
	if (!props.badge) return '#2563EB'
	
	const theme = props.badge.theme || 'blue'
	const preset = themePresets[theme]
	
	if (theme === 'custom') {
		return props.badge.color || '#2563EB'
	}
	
	return preset ? preset.color : '#2563EB'
})

// 事件处理
const handleBadgeClick = () => {
	emit('badge-click', props.badge)
}

const handleTitleClick = () => {
	emit('title-click', props.title)
}
</script>

<style lang="scss" scoped>
.top-navbar {
	&--fixed {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
	}
}

.navbar-content {
	.navbar-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 48rpx;

		.navbar-left {
			flex: 1;

			.navbar-title {
				font-size: 42rpx;
				font-weight: bold;
				color: #1f2937;
				line-height: 1.2;
			}
		}
	}

	.navbar-subtitle-row {
		padding: 0 48rpx 16rpx;
		display: flex;
		align-items: center;
		justify-content: space-between;

		.navbar-subtitle {
			font-size: 28rpx;
			color: #6b7280;
			line-height: 1.2;
		}

		.navbar-right {
			display: flex;
			align-items: center;
			justify-content: center;

			.navbar-badge {
				padding: 4rpx 12rpx;
				border-radius: 32rpx;
				font-size: 22rpx;
				font-weight: 500;
				display: flex;
				align-items: center;
				gap: 4rpx;
				transition: all 0.3s ease;

				.badge-text {
					font-size: 26rpx;
				}

				&--blue {
					background: #EFF6FF;
					color: #2563EB;
				}

				&--green {
					background: #D1FAE5;
					color: #10b981;
				}

				&:active {
					transform: scale(0.95);
					opacity: 0.8;
				}
			}
		}
	}
}
</style>
