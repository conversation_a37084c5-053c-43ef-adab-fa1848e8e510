{"designSystem": {"name": "蓉蓉の记账本", "version": "1.0.0", "description": "基于login.html分析的完整设计系统，融合现代毛玻璃效果与绿色主题的记账应用设计规范", "colors": {"primary": {"main": "#4CAF50", "light": "#66BB6A", "dark": "#388E3C", "gradient": "linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)", "description": "主绿色，用于品牌色和主要操作"}, "secondary": {"blue": "#2563EB", "blueLight": "#3B82F6", "green": "#16A34A", "greenLight": "#22C55E", "description": "辅助色系，用于功能分类和状态区分"}, "wechat": {"primary": "#07C160", "hover": "#06AD56", "description": "微信品牌色"}, "text": {"primary": "#181818", "secondary": "#333333", "tertiary": "#374151", "quaternary": "#1F2937", "subtitle": "#7F7F7F", "label": "#B2B2B2", "disabled": "#6B7280", "white": "#FFFFFF", "whiteSecondary": "rgba(255, 255, 255, 0.8)", "description": "文字颜色层级系统"}, "background": {"page": "#F1F4FA", "pageGradient": "linear-gradient(135deg, #E8F5E9 0%, #F8F8F8 100%)", "loginGradient": "linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)", "card": "rgba(255, 255, 255, 0.9)", "cardHover": "rgba(255, 255, 255, 0.95)", "glass": "rgba(255, 255, 255, 0.15)", "glassHover": "rgba(255, 255, 255, 0.25)", "mask": "rgba(0, 0, 0, 0.6)", "description": "背景色系统，包含毛玻璃效果"}, "status": {"success": "#09BE4F", "warning": "#FFB703", "danger": "#FF2B2B", "error": "#EF4444", "info": "#465CFF", "description": "状态色系统"}, "functional": {"border": "#EEEEEE", "borderLight": "rgba(255, 255, 255, 0.2)", "shadow": "rgba(0, 0, 0, 0.1)", "shadowLight": "rgba(0, 0, 0, 0.08)", "shadowMedium": "rgba(0, 0, 0, 0.12)", "description": "功能性颜色"}}, "typography": {"fontFamily": {"primary": "'PingFang SC', 'Helvetica Neue', Arial, sans-serif", "system": "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif", "monospace": "'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace", "digital": "'DigitalNumbers', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif", "description": "字体族系统"}, "fontSize": {"xs": "20rpx", "sm": "24rpx", "base": "28rpx", "md": "32rpx", "lg": "36rpx", "xl": "44rpx", "2xl": "60rpx", "h1": "44rpx", "h2": "36rpx", "h3": "32rpx", "h4": "28rpx", "h5": "24rpx", "description": "字体大小系统，使用rpx单位适配小程序"}, "fontWeight": {"normal": 400, "medium": 500, "semibold": 600, "bold": 700, "extrabold": 800, "description": "字重系统"}, "lineHeight": {"tight": 1.2, "normal": 1.5, "relaxed": 1.8, "description": "行高系统"}}, "spacing": {"xs": "8rpx", "sm": "16rpx", "md": "24rpx", "lg": "32rpx", "xl": "48rpx", "2xl": "64rpx", "3xl": "96rpx", "4xl": "128rpx", "section": "48rpx", "card": "32rpx", "description": "间距系统，基于8rpx网格"}, "borderRadius": {"xs": "4rpx", "sm": "8rpx", "md": "16rpx", "lg": "24rpx", "xl": "32rpx", "2xl": "48rpx", "full": "50%", "card": "32rpx", "button": "12rpx", "tag": "20rpx", "description": "圆角系统"}, "shadows": {"sm": "0 2rpx 8rpx rgba(0, 0, 0, 0.05)", "md": "0 8rpx 32rpx rgba(0, 0, 0, 0.1)", "lg": "0 16rpx 64rpx rgba(0, 0, 0, 0.1)", "xl": "0 24rpx 80rpx rgba(0, 0, 0, 0.15)", "card": "0 8rpx 40rpx rgba(0, 0, 0, 0.08)", "cardHover": "0 16rpx 64rpx rgba(0, 0, 0, 0.12)", "primary": "0 16rpx 64rpx rgba(76, 175, 80, 0.3)", "primaryHover": "0 24rpx 80rpx rgba(76, 175, 80, 0.4)", "wechat": "0 4rpx 16rpx rgba(7, 193, 96, 0.3)", "wechatHover": "0 6rpx 20rpx rgba(7, 193, 96, 0.4)", "description": "阴影系统，包含品牌色阴影"}, "effects": {"blur": {"sm": "blur(10rpx)", "md": "blur(20rpx)", "lg": "blur(40rpx)", "description": "毛玻璃模糊效果"}, "transition": {"fast": "all 0.15s ease", "normal": "all 0.2s ease", "slow": "all 0.3s ease", "description": "过渡动画时长"}, "transform": {"hover": "translateY(-4rpx)", "active": "scale(0.95)", "description": "变换效果"}}, "components": {"button": {"primary": {"background": "linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%)", "color": "#FFFFFF", "borderRadius": "32rpx", "padding": "32rpx 48rpx", "fontSize": "32rpx", "fontWeight": 600, "shadow": "0 16rpx 48rpx rgba(76, 175, 80, 0.3)", "hoverShadow": "0 24rpx 64rpx rgba(76, 175, 80, 0.4)", "activeTransform": "scale(0.95)"}, "wechat": {"background": "#07C160", "color": "#FFFFFF", "borderRadius": "12rpx", "padding": "32rpx 48rpx", "fontSize": "32rpx", "fontWeight": 500, "shadow": "0 8rpx 32rpx rgba(7, 193, 96, 0.3)", "hoverBackground": "#06AD56", "hoverShadow": "0 12rpx 40rpx rgba(7, 193, 96, 0.4)"}, "glass": {"background": "rgba(255, 255, 255, 0.8)", "backdropFilter": "blur(20rpx)", "border": "2rpx solid rgba(255, 255, 255, 0.2)", "borderRadius": "32rpx", "padding": "32rpx", "hoverBackground": "rgba(255, 255, 255, 0.9)", "activeTransform": "scale(0.95)"}}, "card": {"default": {"background": "rgba(255, 255, 255, 0.9)", "backdropFilter": "blur(20rpx)", "border": "2rpx solid rgba(255, 255, 255, 0.2)", "borderRadius": "32rpx", "padding": "32rpx", "shadow": "0 8rpx 40rpx rgba(0, 0, 0, 0.08)", "hoverShadow": "0 16rpx 64rpx rgba(0, 0, 0, 0.12)", "hoverTransform": "translateY(-4rpx)"}, "income": {"background": "linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%)", "color": "#FFFFFF", "borderRadius": "48rpx", "padding": "48rpx", "shadow": "0 16rpx 64rpx rgba(76, 175, 80, 0.3)", "hoverShadow": "0 24rpx 80rpx rgba(76, 175, 80, 0.4)", "hoverTransform": "translateY(-4rpx)"}, "feature": {"background": "rgba(255, 255, 255, 0.15)", "backdropFilter": "blur(20rpx)", "border": "1px solid rgba(255, 255, 255, 0.2)", "borderRadius": "16rpx", "padding": "24rpx 16rpx", "shadow": "0 8rpx 32rpx rgba(0, 0, 0, 0.1)", "hoverBackground": "rgba(255, 255, 255, 0.25)", "hoverShadow": "0 12rpx 40rpx rgba(0, 0, 0, 0.15)", "hoverTransform": "translateY(-4rpx)"}}, "icon": {"container": {"borderRadius": "50%", "display": "flex", "alignItems": "center", "justifyContent": "center"}, "sizes": {"sm": "64rpx", "md": "96rpx", "lg": "120rpx"}, "backgrounds": {"blue": "#DBEAFE", "green": "#DCFCE7", "white": "rgba(255, 255, 255, 0.9)", "primary": "rgba(76, 175, 80, 0.1)"}}, "tag": {"work": {"background": "rgba(37, 99, 235, 0.1)", "color": "#2563EB", "borderRadius": "20rpx", "padding": "8rpx 16rpx", "fontSize": "20rpx", "fontWeight": 600}, "piece": {"background": "rgba(22, 163, 74, 0.1)", "color": "#16A34A", "borderRadius": "20rpx", "padding": "8rpx 16rpx", "fontSize": "20rpx", "fontWeight": 600}}, "input": {"default": {"background": "rgba(255, 255, 255, 0.9)", "border": "2rpx solid rgba(255, 255, 255, 0.2)", "borderRadius": "16rpx", "padding": "24rpx 32rpx", "fontSize": "28rpx", "focusBorder": "2rpx solid #4CAF50"}}, "checkbox": {"default": {"width": "36rpx", "height": "36rpx", "border": "4rpx solid rgba(255, 255, 255, 0.6)", "borderRadius": "8rpx", "background": "transparent", "checkedBackground": "#FFFFFF", "checkedBorder": "#FFFFFF"}}}, "layout": {"container": {"maxWidth": "750rpx", "padding": "0 32rpx", "margin": "0 auto"}, "grid": {"columns": 12, "gap": "32rpx", "features": "repeat(3, 1fr)", "records": "1fr 1fr"}, "spacing": {"section": "48rpx", "component": "32rpx", "element": "24rpx", "text": "16rpx"}, "safeArea": {"top": "88rpx", "bottom": "68rpx", "statusBar": "44rpx", "navbar": "88rpx"}}, "animations": {"floating": {"keyframes": "0%, 100% { transform: translateY(0px) rotate(0deg); } 50% { transform: translateY(-20px) rotate(180deg); }", "duration": "6s", "timing": "ease-in-out", "iteration": "infinite"}, "fadeIn": {"from": "opacity: 0; transform: translateY(20rpx);", "to": "opacity: 1; transform: translateY(0);", "duration": "0.3s", "timing": "ease-out"}, "scaleIn": {"from": "opacity: 0; transform: scale(0.9);", "to": "opacity: 1; transform: scale(1);", "duration": "0.2s", "timing": "ease-out"}}, "patterns": {"glassCard": {"description": "毛玻璃卡片模式", "properties": {"background": "rgba(255, 255, 255, 0.9)", "backdropFilter": "blur(20rpx)", "border": "2rpx solid rgba(255, 255, 255, 0.2)", "borderRadius": "32rpx", "shadow": "0 8rpx 40rpx rgba(0, 0, 0, 0.08)"}}, "gradientBackground": {"description": "渐变背景模式", "properties": {"primary": "linear-gradient(135deg, #4CAF50 0%, #66BB6A 100%)", "page": "linear-gradient(135deg, #E8F5E9 0%, #F8F8F8 100%)", "income": "linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%)"}}, "floatingElements": {"description": "浮动装饰元素", "properties": {"background": "rgba(255, 255, 255, 0.1)", "borderRadius": "50%", "animation": "float 6s ease-in-out infinite", "sizes": ["60px", "40px", "80px"], "positions": ["10% 10%", "20% 85%", "80% 5%"]}}}, "designPrinciples": {"visual": {"clarity": "使用高对比度确保文字清晰可读", "hierarchy": "通过字体大小、颜色、间距建立清晰的信息层级", "consistency": "保持组件样式和交互的一致性"}, "interaction": {"feedback": "所有交互元素提供视觉反馈（hover、active状态）", "accessibility": "确保足够的点击区域和对比度", "performance": "使用CSS变换而非改变布局属性来实现动画"}, "branding": {"color": "以绿色为主色调，体现记账应用的财务属性", "glass": "大量使用毛玻璃效果营造现代感", "warmth": "通过圆角和柔和的阴影营造亲和力"}}, "implementation": {"uniapp": {"units": "使用rpx单位确保多端适配", "components": "优先使用Vue 3 Composition API", "styling": "使用SCSS预处理器管理样式变量"}, "responsive": {"breakpoints": {"mobile": "750rpx", "tablet": "1024rpx"}, "strategy": "移动优先，渐进增强"}}}}