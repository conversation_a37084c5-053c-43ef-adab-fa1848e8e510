<template>
	<fui-tabbar 
		:tabBar="tabBar" 
		:current="current"
		size='24' 
		fontWeight="600" 
		color="#9B9B9B" 
		selectedColor="#3B82F6"
		background="#FFFFFF"
		:isBorder="false"
		:fixedHeight="true"
		@init="inttTabbar"
		@click="tabBarClick"
		>
	</fui-tabbar>
</template> 

<script setup>
	import { ref } from 'vue';
	
	const tabBar =  [
		{
			"url": "pages/home/<USER>",
			"text": "记录",
			"iconPath": "/static/tabbar/fa--calendar-check-o.png",
			"selectedIconPath": "/static/tabbar/fa--calendar-check-o-A.png"
		},
		{
			"url": "pages/statistics/index",
			"text": "统计",
			"iconPath": "/static/tabbar/fa--bar-chart.png",
			"selectedIconPath": "/static/tabbar/fa--bar-chart-A.png"
		},
		{
			"url": "pages/profile/index",
			"text": "我的",
			"iconPath": "/static/tabbar/fa--user.png",
			"selectedIconPath": "/static/tabbar/fa--user-A.png"
		}
	]
	
	// 当前索引
	const current = ref(0)
	// 初始化索引
	let currentPages = getCurrentPages()
	let page = currentPages[currentPages.length - 1]
	const nowPath = page.route;
	//获取页面路径
	tabBar.forEach((item,index)=>{
	    if(nowPath == item.url){
			current.value = index
	    }
	})
	
	const tabBarClick = async (tabbarItem) => {
		if (nowPath !== tabBar[tabbarItem.index].url) {
			uni.switchTab({
				url: '/' + tabBar[tabbarItem.index].url
			});
		}
	}
	
	// 初始化tabbar返回页面高度
	const emit = defineEmits(['inttTabbar'])
	const inttTabbar = (initData) => {
		emit("inttTabbar", initData)
	}
</script>