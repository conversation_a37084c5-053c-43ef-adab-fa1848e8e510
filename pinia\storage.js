import { defineStore } from 'pinia'
import { reactive } from 'vue'

// 本地，仓库变量 封装
export const useStoragePiniaStore = defineStore('storagePinia', () => {
	let lifeData = {};
	try{
		// 尝试获取本地是否存在lifeData变量，第一次启动APP时是不存在的
		lifeData = uni.getStorageSync('lifeData');
	}catch(e){
		
	}
	let saveStateKeys = ['pinia_user'];
	
	const storageDta = reactive({
		pinia_user: lifeData.pinia_user ? lifeData.pinia_user : {},
	})
	
	// 保存本地数据
	const savePiniaData = (dataKey, value) => {
		// 判断是否多层级调用，storageDta中为对象存在的情况，诸如user.info.score = 1
		let nameArr = dataKey.split('.');
		let saveKey = '';
		let len = nameArr.length;
		if(nameArr.length >= 2) {
			let obj = storageDta[nameArr[0]];
			for(let i = 1; i < len - 1; i ++) {
				obj = obj[nameArr[i]];
			}
			obj[nameArr[len - 1]] = value;
			saveKey = nameArr[0];
		} else {
			
			storageDta[dataKey] = value;
			saveKey = dataKey;
		}
		// 保存变量到本地，见顶部函数定义
		saveLifeData(saveKey, storageDta[saveKey])
	}
	
	// 保存变量到本地存储中
	const saveLifeData = (key, value) => {
		// 判断变量名是否在需要存储的数组中
		if(saveStateKeys.indexOf(key) != -1) {
			// 获取本地存储的lifeData对象，将变量添加到对象中
			let tmp = uni.getStorageSync('lifeData');
			// 第一次打开APP，不存在lifeData变量，故放一个{}空对象
			tmp = tmp ? tmp : {};
			tmp[key] = value;
			// 执行这一步后，所有需要存储的变量，都挂载在本地的lifeData对象中
			uni.setStorageSync('lifeData', tmp);
		}
	}
	return { 
		storageDta, 
		savePiniaData 
	};
});