你是一位资深的前端工程师、UI/UX设计师和产品经理，需要为微信小程序《蓉蓉の记账本》创建完整的高保真原型设计。
请基于已有的设计风格参考 `D:\A_MY\rongrongdecashbook\doc\设计风格.json`
原型图输入到 `D:\A_MY\rongrongdecashbook\prototype\home.html`，按照以下具体步骤完成原型设计：

目前只需要设计一个《主页》页面，帮我设计一个美观好看的主页

原型输出规则：
1、用户体验分析：先分析这个 App 的主要功能和用户需求，确定核心交互逻辑。
2、产品界面规划：作为产品经理，定义关键界面，确保信息架构合理。
3、高保真 UI 设计：作为 UI 设计师，设计贴近真实 iOS/Android 设计规范的界面，使用现代化的 UI 元素，使其具有良好的视觉体验。
4、HTML 原型实现：使用 HTML + Tailwind CSS（或 Bootstrap）生成所有原型界面，并使用 FontAwesome（或其他开源 UI 组件）让界面更加精美、接近真实的 App 设计。拆分代码文件，保持结构清晰：
5、每个界面应作为独立的 HTML 文件存放，例如 home.html、profile.html、settings.html 等。
6.图标使用：Lucide Static CDN 方式引入，如`https://unpkg.com/lucide-static@latest/icons/XXX.svg`。
7.尺寸设计按375px宽度设计，只考虑手机用户，不做响应式设计
8.设计风格:基于已有的设计风格 参考 `D:\A_MY\rongrongdecashbook\doc\设计风格.json`



请参考以下两个文件来完成页面的开发：

1. **UI设计参考**：`d:\A_outsource\yysls-ranking-app/prototype\system-management.html` - 作为UI设计和布局的原型参考
2. **代码风格参考**：`D:\A_outsource\yysls-ranking-app\subPackages\admin\announcement-management.vue` - 作为代码结构、组件写法和样式风格的参考

**开发要求**：
- 在 `subPackages/admin/system-management.vue` 路径下创建页面
- 这个页面将作为admin分包的一部分进行分包加载
- 使用uniapp vue3 setup语法模式开发
- 只需要考虑微信小程序端的兼容性
- 保持与现有代码风格的一致性
- 确保页面布局和交互符合原型图的设计要求
- 不考虑响应式设计，只考虑微信小程序的界面展示
- 不考虑原型图的加载动画、可以保留一定交互动画，添加一定的交互动画

请先分析参考文件的内容，然后创建符合要求的页面组件。