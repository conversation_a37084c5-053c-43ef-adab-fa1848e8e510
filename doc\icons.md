## 图标列表

| 图标名称 | CDN链接 | 使用模块 | 颜色规范 | 尺寸规范 | 语义说明 |
|---------|---------|----------|----------|----------|----------|
| book-open | https://unpkg.com/lucide-static@latest/icons/book-open.svg | 登录页面 | #4CAF50 | 64px | 应用Logo，代表记账本 |
| clock | https://unpkg.com/lucide-static@latest/icons/clock.svg | 登录页面 | #2563eb | 20px | 工时记录功能图标 |
| package | https://unpkg.com/lucide-static@latest/icons/package.svg | 登录页面 | #16a34a | 20px | 计件记录功能图标 |
| trending-up | https://unpkg.com/lucide-static@latest/icons/trending-up.svg | 登录页面 | #fb923c | 20px | 数据统计功能图标 |
| message-circle | https://unpkg.com/lucide-static@latest/icons/message-circle.svg | 登录页面 | #ffffff | 24px | 微信登录按钮图标 |
| check | https://unpkg.com/lucide-static@latest/icons/check.svg | 登录页面 | #4CAF50 | 10px | 勾选框勾选状态图标 |
| loader-2 | https://unpkg.com/lucide-static@latest/icons/loader-2.svg | 登录页面 | #ffffff | 24px | 加载状态图标 |

```javascript
// 图标列表
const icons = [
    'book-open',
    'clock',
    'package',
    'trending-up',
    'message-circle',
    'check',
    'loader-2'
];