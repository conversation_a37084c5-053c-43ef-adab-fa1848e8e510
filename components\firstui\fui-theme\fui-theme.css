/*
  FirstUI组件内置的基础变量
  1.如果你是组件使用者，你可以通过修改这些变量的值来定制自己的组件主题，实现自定义主题功能
  2.如果全局修改需要在项目根目录下App.vue文件中引入此css文件
  3.如果组件中有props属性是针对颜色设置（默认为空值），则优先级：props变量（如果有传值）> 全局主题色
*/

/* #ifndef APP-NVUE */
page {
	/* 行为相关颜色 */
	--fui-color-primary: #465CFF;
	--fui-color-success: #09BE4F;
	--fui-color-warning: #FFB703;
	--fui-color-danger: #FF2B2B;
	--fui-color-purple: #6831FF;

	/* 文字基本颜色、其他辅助色 */
	/* 用于重量级文字信息、标题 */
	--fui-color-title: #181818;
	/* 用于普通级段落信息、引导词 */
	--fui-color-section: #333333;
	/* 用于次要标题内容 */
	--fui-color-subtitle: #7F7F7F;
	/* 用于底部标签、描述、次要文字信息 */
	--fui-color-label: #B2B2B2;
	/* 用于辅助、次要信息、禁用文字等。如：待输入状态描述文字，已点击按钮文字 */
	--fui-color-minor: #CCCCCC;
	--fui-color-white: #FFFFFF;
	/* 链接颜色 */
	--fui-color-link: #465CFF;


	/* 背景颜色 */
	--fui-bg-color: #ffffff;
	/* 页面背景底色 */
	--fui-bg-color-grey: #F1F4FA;
	/* 内容模块底色 */
	--fui-bg-color-content: #F8F8F8;
	--fui-bg-color-red: rgba(255, 43, 43, .05);
	--fui-bg-color-yellow: rgba(255, 183, 3, .1);
	--fui-bg-color-purple: rgba(104, 49, 255, .05);
	--fui-bg-color-green: rgba(9, 190, 79, .05);
	/* 点击背景色 */
	--fui-bg-color-hover: rgba(0, 0, 0, 0.2);
	/* 遮罩颜色 */
	--fui-bg-color-mask: rgba(0, 0, 0, 0.6);


	/* 边框颜色 */
	--fui-color-border: #EEEEEE;

	/* 阴影颜色 */
	--fui-color-shadow: rgba(2, 4, 38, 0.05);

	/*禁用态的透明度 */
	--fui-opacity-disabled: 0.5;

	/* 图标尺寸 */
	--fui-img-size-sm: 48rpx;
	--fui-img-size-base: 56rpx;
	--fui-img-size-middle: 64rpx;
	--fui-img-size-lg: 96rpx;

	/* 图片尺寸 */
	--fui-img-sm: 60rpx;
	--fui-img-base: 120rpx;
	--fui-img-lg: 240rpx;

	/* Border Radius */
	--fui-border-radius-sm: 16rpx;
	--fui-border-radius-base: 24rpx;
	--fui-border-radius-lg: 48rpx;
	--fui-border-radius-circle: 50%;

	/* 水平间距 */
	--fui-spacing-row-sm: 16rpx;
	--fui-spacing-row-base: 24rpx;
	--fui-spacing-row-lg: 32rpx;

	/* 垂直间距 */
	--fui-spacing-col-sm: 8rpx;
	--fui-spacing-col-base: 16rpx;
	--fui-spacing-col-lg: 24rpx;
}
/* #endif */
