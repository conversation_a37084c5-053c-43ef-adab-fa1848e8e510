import App from './App';

import { createSSRApp } from 'vue';
// 仓库
import { store } from "@/pinia/index.js"
// 公共组件
import publicModule from "@/components/common/public-module.vue";

// http请求库
import Request from '@/utils/luch-request/index.js';
uni.$http = new Request()
// 全局分享配置
import { share } from './utils/share';


export function createApp() {
	
  const app = createSSRApp(App);
  app.use(store);
  app.component('publicModule', publicModule);
  app.mixin(share)
  
  return {
    app
  }
}
