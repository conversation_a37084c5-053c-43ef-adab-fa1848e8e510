<template>
	<view>
		<!-- 公共模块 -->
		<publicModule></publicModule>

		<!-- 顶部导航栏 -->
		<top-navbar title="收入统计" subtitle="数据洞察，收益一目了然" :badge="{
				text: '统计',
				icon: 'icon-zhexiantu',
				theme: 'green',
				iconSize: 28
			}" @badge-click="handleStatsClick" />

		<!-- 统计页面内容 -->
		<view class="statistics-container">
			<!-- 内容区域，添加顶部间距避免被固定导航栏遮挡 -->
			<view class="content-area" :style="{ paddingTop: (statusBarHeight + navBarHeight + 40) + 'px' }">
				<!-- 时间筛选 -->
				<view class="time-filter">
					<view v-for="period in timePeriods" :key="period.value" class="period-btn"
						:class="{ active: selectedPeriod === period.value }" @click="selectPeriod(period.value)">
						{{ period.text }}
					</view>
				</view>

				<!-- 收入概览 -->
				<view class="income-overview">
					<view class="stats-card">
						<view class="stats-header">
							<view class="stats-title">{{ currentPeriodText }}收入概览</view>
							<view class="period-badge">{{ currentDateRange }}</view>
						</view>

						<view class="stats-content">
							<view class="total-income">
								<text class="income-label">总收入</text>
								<text class="income-amount">¥{{ totalIncome }}</text>
							</view>

							<view class="average-income">
								<text class="income-label">平均日收入</text>
								<text class="income-amount">¥183.64</text>
							</view>

							<view class="income-breakdown">
								<view class="breakdown-item">
									<view class="breakdown-indicator blue"></view>
									<text class="breakdown-label">工时收入</text>
									<view class="breakdown-right">
										<text class="breakdown-amount">¥{{ workHoursIncome }}</text>
										<text class="breakdown-percent">({{ workHoursPercent }}%)</text>
									</view>
								</view>
								<view class="breakdown-item">
									<view class="breakdown-indicator green"></view>
									<text class="breakdown-label">计件收入</text>
									<view class="breakdown-right">
										<text class="breakdown-amount">¥{{ pieceWorkIncome }}</text>
										<text class="breakdown-percent">({{ pieceWorkPercent }}%)</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 工作统计 -->
				<view class="work-statistics">
					<view class="section-title">
						<sx-svg name="chart-bar" :size="24" color="#1f2937"></sx-svg>
						<text class="title-text">工作统计</text>
					</view>
					<view class="statistics-grid">
						<view v-for="statistic in workStatistics" :key="statistic.id" class="statistic-card" :style="{ backgroundColor: statistic.bgColor }">
							<view class="statistic-content">
								<sx-svg :name="statistic.icon" :size="24" :color="statistic.iconColor" class="statistic-icon"></sx-svg>
								<text class="statistic-label">{{ statistic.label }}</text>
								<text class="statistic-value" :style="{ color: statistic.valueColor }">{{ statistic.value }}</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 收入趋势 -->
				<view class="income-trend">
					<view class="trend-card">
						<view class="trend-header">
							<view class="trend-title-row">
								<text class="trend-title">收入趋势</text>
								<sx-svg name="ellipsis" :size="20" color="rgba(255, 255, 255, 0.7)"></sx-svg>
							</view>
						</view>

						<!-- 图表占位区域 -->
						<view class="chart-placeholder">
							<view class="chart-content">
								<sx-svg name="trending-up" :size="48" color="#3b82f6"></sx-svg>
								<text class="chart-text">收入趋势图表</text>
							</view>
						</view>

						<!-- 图例 -->
						<view class="chart-legend">
							<view class="legend-item">
								<view class="legend-color" style="background-color: #3b82f6;"></view>
								<text class="legend-text">工时</text>
							</view>
							<view class="legend-item">
								<view class="legend-color" style="background-color: #10b981;"></view>
								<text class="legend-text">计件</text>
							</view>
						</view>
					</view>
				</view>

				<!-- 详细记录 -->
				<view class="recent-records">
					<view class="section-header">
						<view class="section-title">
							<sx-svg name="list" :size="24" color="#1f2937"></sx-svg>
							<text class="title-text">详细记录</text>
						</view>
						<text class="view-all-btn" @click="viewAllRecords">查看全部</text>
					</view>

					<view class="records-list">
						<view v-for="record in recentRecords" :key="record.id" class="record-item"
							@click="viewRecordDetail(record)">
							<view class="record-left">
								<view class="record-date">
									<text class="date-number">{{ record.day }}</text>
									<text class="date-month">01月</text>
								</view>
								<view class="record-info">
									<text class="record-title">{{ record.workInfo }}</text>
									<text class="record-desc">{{ record.description }}</text>
								</view>
							</view>
							<view class="record-right">
								<text class="record-amount">¥{{ record.amount }}</text>
								<view class="record-indicators">
									<view class="indicator-dot blue"></view>
									<view class="indicator-dot green"></view>
								</view>
							</view>
						</view>

						<!-- 周汇总 -->
						<view class="summary-item">
							<view class="summary-left">
								<view class="summary-icon">
									<sx-svg name="calculator" :size="24" color="#ffffff"></sx-svg>
								</view>
								<view class="summary-info">
									<text class="summary-title">本周汇总</text>
									<text class="summary-desc">工作5天，效率很高</text>
								</view>
							</view>
							<view class="summary-right">
								<text class="summary-amount">¥{{ weeklyTotal }}</text>
								<text class="summary-label">周收入</text>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 底部安全区域 -->
			<view class="bottom-safe"></view>
		</view>

		<!-- 底部导航 -->
		<tabbar></tabbar>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		onMounted
	} from 'vue'
	import tabbar from "@/components/common/l-tabbar.vue"
	import topNavbar from "@/components/common/top-navbar.vue"
	import sxSvg from "@/components/sx-svg/sx-svg.vue"
	import {
		useSystemInfo
	} from '@/hooks/useSystemInfo.js'

	// 隐藏系统tabbar
	uni.hideTabBar()

	// 使用系统信息 hook
	const {
		navBarHeight,
		statusBarHeight,
		getSystemInfo
	} = useSystemInfo()

	// 响应式数据
	const selectedPeriod = ref('month')
	const totalIncome = ref('12,500.00')
	const workHoursIncome = ref('10,000.00')
	const pieceWorkIncome = ref('2,500.00')
	const growthRate = ref('23.5')
	const weeklyTotal = ref('2,800')
	const chartLoading = ref(false)

	const timePeriods = ref([{
			text: '今天',
			value: 'today'
		},
		{
			text: '本周',
			value: 'week'
		},
		{
			text: '本月',
			value: 'month'
		},
		{
			text: '本年',
			value: 'year'
		}
	])

	const workStatistics = ref([{
			id: 1,
			label: '总工时',
			value: '29.5小时',
			icon: 'clock',
			iconColor: '#2563eb',
			valueColor: '#2563eb',
			bgColor: '#dbeafe'
		},
		{
			id: 2,
			label: '总计件',
			value: '79个',
			icon: 'package',
			iconColor: '#16a34a',
			valueColor: '#16a34a',
			bgColor: '#dcfce7'
		},
		{
			id: 3,
			label: '工作天数',
			value: '7天',
			icon: 'calendar',
			iconColor: '#3b82f6',
			valueColor: '#3b82f6',
			bgColor: '#ede9fe'
		},
		{
			id: 4,
			label: '平均效率',
			value: '4.2小时/天',
			icon: 'trending-up',
			iconColor: '#f59e0b',
			valueColor: '#f59e0b',
			bgColor: '#fef3c7'
		}
	])

	const chartData = ref([{
			label: '周一',
			amount: 320
		},
		{
			label: '周二',
			amount: 420
		},
		{
			label: '周三',
			amount: 280
		},
		{
			label: '周四',
			amount: 520
		},
		{
			label: '周五',
			amount: 380
		},
		{
			label: '周六',
			amount: 220
		},
		{
			label: '周日',
			amount: 480
		}
	])



	const recentRecords = ref([{
			id: 1,
			day: '15',
			workInfo: '工时3h + 计件17个',
			description: '数据录入 · 包装盒制作',
			amount: '175.00'
		},
		{
			id: 2,
			day: '14',
			workInfo: '工时4h + 计件12个',
			description: '文档整理 · 产品包装',
			amount: '180.00'
		},
		{
			id: 3,
			day: '13',
			workInfo: '工时2.5h + 计件8个',
			description: '系统维护 · 质量检查',
			amount: '115.00'
		}
	])

	// 计算属性
	const currentPeriodText = computed(() => {
		const periodMap = {
			today: '今日',
			week: '本周',
			month: '本月',
			year: '本年'
		}
		return periodMap[selectedPeriod.value] || '本月'
	})

	const currentDateRange = computed(() => {
		const dateMap = {
			today: '2024年1月15日',
			week: '1月9日-15日',
			month: '2024年1月',
			year: '2024年'
		}
		return dateMap[selectedPeriod.value] || '2024年1月'
	})

	const workHoursPercent = computed(() => {
		const total = parseFloat(totalIncome.value.replace(/,/g, ''))
		const workHours = parseFloat(workHoursIncome.value.replace(/,/g, ''))
		return Math.round((workHours / total) * 100)
	})

	const pieceWorkPercent = computed(() => {
		const total = parseFloat(totalIncome.value.replace(/,/g, ''))
		const pieceWork = parseFloat(pieceWorkIncome.value.replace(/,/g, ''))
		return Math.round((pieceWork / total) * 100)
	})

	// 方法
	const selectPeriod = (period) => {
		chartLoading.value = true
		selectedPeriod.value = period

		// 模拟数据加载延时，提升用户体验
		setTimeout(() => {
			updateStatsData(period)
			chartLoading.value = false
		}, 300)
	}

	const getPreviousPeriod = () => {
		const periodMap = {
			today: '昨天',
			week: '周',
			month: '月',
			year: '年'
		}
		return periodMap[selectedPeriod.value] || '月'
	}

	const updateStatsData = (period) => {
		// 根据不同时间段更新数据
		const dataMap = {
			today: {
				total: '400.00',
				workHours: '350.00',
				pieceWork: '50.00',
				growth: '15.2',
				chartData: [
					{ label: '上午', amount: 180 },
					{ label: '下午', amount: 220 }
				]
			},
			week: {
				total: '2,800.00',
				workHours: '2,400.00',
				pieceWork: '400.00',
				growth: '12.8',
				chartData: [
					{ label: '周一', amount: 320 },
					{ label: '周二', amount: 420 },
					{ label: '周三', amount: 280 },
					{ label: '周四', amount: 520 },
					{ label: '周五', amount: 380 },
					{ label: '周六', amount: 220 },
					{ label: '周日', amount: 480 }
				]
			},
			month: {
				total: '12,500.00',
				workHours: '10,000.00',
				pieceWork: '2,500.00',
				growth: '23.5',
				chartData: [
					{ label: '第1周', amount: 2800 },
					{ label: '第2周', amount: 3200 },
					{ label: '第3周', amount: 2900 },
					{ label: '第4周', amount: 3600 }
				]
			},
			year: {
				total: '150,000.00',
				workHours: '120,000.00',
				pieceWork: '30,000.00',
				growth: '18.7',
				chartData: [
					{ label: '1月', amount: 12500 },
					{ label: '2月', amount: 11800 },
					{ label: '3月', amount: 13200 },
					{ label: '4月', amount: 12900 },
					{ label: '5月', amount: 13800 },
					{ label: '6月', amount: 14200 }
				]
			}
		}

		const data = dataMap[period]
		if (data) {
			totalIncome.value = data.total
			workHoursIncome.value = data.workHours
			pieceWorkIncome.value = data.pieceWork
			growthRate.value = data.growth
			// 更新图表数据
			chartData.value = data.chartData
		}
	}

	const viewAllRecords = () => {
		uni.showToast({
			title: '查看全部记录',
			icon: 'none'
		})
	}

	const viewRecordDetail = (record) => {
		uni.showToast({
			title: `查看${record.date}详情`,
			icon: 'none'
		})
	}

	const handleStatsClick = () => {
		uni.showToast({
			title: '统计徽章点击',
			icon: 'none'
		})
	}

	// 页面生命周期
	onMounted(() => {
		// 获取系统信息
		getSystemInfo()

		// 模拟实时数据更新
		setInterval(() => {
			const currentValue = parseFloat(totalIncome.value.replace(/,/g, ''))
			const newValue = currentValue + (Math.random() * 100 - 50)
			if (newValue > 0) {
				totalIncome.value = newValue.toLocaleString('zh-CN', {
					minimumFractionDigits: 2,
					maximumFractionDigits: 2
				})
			}
		}, 10000)
	})
</script>

<style lang="scss" scoped>
	.statistics-container {
		min-height: 100vh;
		background: #E7EEFF;
		padding-bottom: 160rpx;
	}

	.content-area {
		min-height: 100vh;
	}

	.time-filter {
		padding: 32rpx 48rpx;
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 16rpx;

		.period-btn {
			background: rgba(255, 255, 255, 0.8);
			border: 4rpx solid #e5e7eb;
			border-radius: 24rpx;
			padding: 24rpx;
			text-align: center;
			font-size: 28rpx;
			font-weight: 500;
			color: #374151;

			&.active {
				background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
				border-color: #3b82f6;
				color: #ffffff;
				box-shadow: 0 8rpx 30rpx rgba(59, 130, 246, 0.3);
			}
		}
	}

	.income-overview {
		padding: 16rpx 48rpx 32rpx;

		.stats-card {
			background: rgba(255, 255, 255, 0.9);
			backdrop-filter: blur(20rpx);
			border: 2rpx solid rgba(255, 255, 255, 0.2);
			border-radius: 48rpx;
			padding: 48rpx;
			color: #1f2937;
			box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
			position: relative;
			overflow: hidden;
			transition: all 0.2s ease;

			&:active {
				box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.12);
				transform: translateY(-4rpx);
			}
		}

		.stats-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 32rpx;

			.stats-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #1f2937;
			}

			.period-badge {
				background: rgba(76, 175, 80, 0.1);
				padding: 8rpx 16rpx;
				border-radius: 20rpx;
				font-size: 24rpx;
				color: #4CAF50;
				font-weight: 500;
			}
		}

		.stats-content {
			.total-income, .average-income {
				text-align: center;
				margin-bottom: 32rpx;

				.income-label {
					font-size: 28rpx;
					color: #6b7280;
					margin-bottom: 8rpx;
				}

				.income-amount {
					font-size: 48rpx;
					font-weight: bold;
					color: #1f2937;
					font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
				}
			}

			.income-breakdown {
				display: flex;
				flex-direction: column;
				gap: 24rpx;

				.breakdown-item {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.breakdown-indicator {
						width: 12rpx;
						height: 12rpx;
						border-radius: 50%;
						margin-right: 12rpx;

						&.blue {
							background-color: #3b82f6;
						}

						&.green {
							background-color: #10b981;
						}
					}

					.breakdown-label {
						flex: 1;
						font-size: 28rpx;
						color: #6b7280;
					}

					.breakdown-right {
						display: flex;
						align-items: center;
						gap: 8rpx;

						.breakdown-amount {
							font-size: 32rpx;
							font-weight: 600;
							color: #1f2937;
							font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
						}

						.breakdown-percent {
							font-size: 24rpx;
							color: #9ca3af;
						}
					}
				}
			}
		}
	}

	.work-statistics {
		padding: 16rpx 48rpx 32rpx;

		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 32rpx;

			.title-text {
				font-size: 32rpx;
				font-weight: 600;
				color: #1f2937;
				margin-left: 16rpx;
			}
		}

		.statistics-grid {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: 24rpx;
		}

		.statistic-card {
			border-radius: 24rpx;
			padding: 32rpx;
			text-align: center;
			transition: all 0.2s ease;

			&:active {
				transform: translateY(-2rpx);
				box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
			}

			.statistic-content {
				display: flex;
				flex-direction: column;
				align-items: center;
				gap: 16rpx;

				.statistic-icon {
					margin-bottom: 8rpx;
				}

				.statistic-label {
					font-size: 28rpx;
					color: #6b7280;
					font-weight: 500;
				}

				.statistic-value {
					font-size: 40rpx;
					font-weight: bold;
					letter-spacing: -0.5rpx;
				}
			}
		}
	}

	.income-trend {
		padding: 16rpx 48rpx 32rpx;

		.trend-card {
			background: rgba(255, 255, 255, 0.9);
			backdrop-filter: blur(20rpx);
			border: 2rpx solid rgba(255, 255, 255, 0.2);
			border-radius: 48rpx;
			padding: 48rpx;
			color: #1f2937;
			box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
			position: relative;
			overflow: hidden;
			transition: all 0.2s ease;

			&:active {
				box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.12);
				transform: translateY(-4rpx);
			}
		}

		.trend-header {
			margin-bottom: 32rpx;

			.trend-title-row {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.trend-title {
					font-size: 32rpx;
					font-weight: 600;
					color: #1f2937;
				}
			}
		}

		.chart-placeholder {
			background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
			border: 4rpx dashed #bae6fd;
			border-radius: 24rpx;
			height: 320rpx;
			position: relative;
			overflow: hidden;

			.chart-content {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				height: 100%;
				gap: 16rpx;

				.chart-text {
					font-size: 28rpx;
					color: #6b7280;
				}
			}
		}

		.chart-legend {
			display: flex;
			justify-content: center;
			gap: 48rpx;
			margin-top: 32rpx;

			.legend-item {
				display: flex;
				align-items: center;
				gap: 16rpx;

				.legend-color {
					width: 24rpx;
					height: 8rpx;
					border-radius: 4rpx;
				}

				.legend-text {
					font-size: 28rpx;
					color: #6b7280;
				}
			}
		}
	}

	.recent-records {
		padding: 16rpx 48rpx 32rpx;

		.section-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 32rpx;

			.section-title {
				display: flex;
				align-items: center;

				.title-text {
					font-size: 32rpx;
					font-weight: 600;
					color: #1f2937;
					margin-left: 16rpx;
				}
			}

			.view-all-btn {
				color: #2563eb;
				font-size: 28rpx;
				font-weight: 500;
			}
		}

		.records-list {
			.record-item {
				background: rgba(255, 255, 255, 0.9);
				backdrop-filter: blur(20rpx);
				border: 2rpx solid rgba(255, 255, 255, 0.2);
				border-radius: 48rpx;
				padding: 48rpx;
				margin-bottom: 24rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				position: relative;
				overflow: hidden;
				transition: all 0.2s ease;
				border-bottom: 2rpx solid #f3f4f6;

				&:last-child {
					border-bottom: none;
				}

				&:active {
					box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.12);
					transform: translateY(-4rpx);
				}

				.record-left {
					display: flex;
					align-items: center;
					flex: 1;
					min-width: 0;

					.record-date {
						text-align: center;
						margin-right: 24rpx;
						flex-shrink: 0;

						.date-number {
							font-size: 36rpx;
							font-weight: bold;
							color: #1f2937;
							margin-bottom: 4rpx;
						}

						.date-month {
							font-size: 24rpx;
							color: #9ca3af;
						}
					}

					.record-info {
						flex: 1;
						min-width: 0;

						.record-title {
							font-size: 32rpx;
							font-weight: 600;
							color: #1f2937;
							margin-bottom: 8rpx;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.record-desc {
							font-size: 28rpx;
							color: #6b7280;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}
					}
				}

				.record-right {
					text-align: right;
					flex-shrink: 0;
					min-width: 120rpx;

					.record-amount {
						font-size: 32rpx;
						font-weight: 600;
						color: #1f2937;
						margin-bottom: 8rpx;
						white-space: nowrap;
						font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
					}

					.record-indicators {
						display: flex;
						align-items: center;
						justify-content: flex-end;
						gap: 8rpx;

						.indicator-dot {
							width: 16rpx;
							height: 16rpx;
							border-radius: 50%;

							&.blue {
								background-color: #3b82f6;
							}

							&.green {
								background-color: #10b981;
							}
						}
					}
				}
			}

			.summary-item {
				background: linear-gradient(to right, #f9fafb, #dbeafe);
				border-radius: 48rpx;
				padding: 48rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				.summary-left {
					display: flex;
					align-items: center;
					flex: 1;
					min-width: 0;

					.summary-icon {
						width: 96rpx;
						height: 96rpx;
						background: linear-gradient(135deg, #6b7280, #4b5563);
						border-radius: 50%;
						display: flex;
						align-items: center;
						justify-content: center;
						margin-right: 24rpx;
						flex-shrink: 0;
					}

					.summary-info {
						flex: 1;
						min-width: 0;

						.summary-title {
							font-size: 32rpx;
							font-weight: 600;
							color: #1f2937;
							margin-bottom: 8rpx;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}

						.summary-desc {
							font-size: 28rpx;
							color: #6b7280;
							white-space: nowrap;
							overflow: hidden;
							text-overflow: ellipsis;
						}
					}
				}

				.summary-right {
					text-align: right;
					flex-shrink: 0;
					min-width: 120rpx;

					.summary-amount {
						font-size: 32rpx;
						font-weight: 600;
						color: #3b82f6;
						margin-bottom: 4rpx;
						white-space: nowrap;
						font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
					}

					.summary-label {
						font-size: 24rpx;
						color: #9ca3af;
						white-space: nowrap;
					}
				}
			}
		}
	}

	.bottom-safe {
		height: 40rpx;
	}
</style>