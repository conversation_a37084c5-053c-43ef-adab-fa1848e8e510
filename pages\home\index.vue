<template>
	<view>
		<!-- 公共模块 -->
		<publicModule></publicModule>

		<!-- 顶部导航栏 -->
		<top-navbar title="记账本" subtitle="今天是美好的一天" :badge="{
				text: '今天',
				icon: 'icon-icon',
				theme: 'blue',
				iconSize: 38
			}" @badge-click="handleTodayClick" />

		<!-- 首页内容 -->
		<view class="home-container">
			<!-- 内容区域，添加顶部间距避免被固定导航栏遮挡 -->
			<view class="content-area" :style="{ paddingTop: (statusBarHeight + navBarHeight + 60) + 'px' }">
				<!-- 今日收入概览 -->
				<view class="income-overview">
					<view class="income-card">
						<view class="income-header">
							<text class="income-title">今日收入概览</text>
						</view>

						<view class="income-details">
							<view class="income-item">
								<text class="income-label">工时收入</text>
								<text class="income-value amount-text">¥{{ todayIncome.workHours }}</text>
							</view>
							<view class="income-item">
								<text class="income-label">计件收入</text>
								<text class="income-value amount-text">¥{{ todayIncome.pieceWork }}</text>
							</view>
						</view>

						<view class="income-divider"></view>

						<view class="income-total">
							<view class="total-row">
								<text class="total-label">总收入</text>
								<text class="total-value amount-text">¥{{ todayIncome.total }}</text>
							</view>
							<view class="growth-indicator">
								<text class="growth-text">比昨天</text>
								<view class="growth-content">
									<sx-svg name="trending-up" :size="24" color="#FF0015"></sx-svg>
									<text class="growth-number">增长 {{ growthRate }}%</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 快速记录 -->
				<view class="quick-record">
					<view class="section-title">
						<sx-svg name="zap" :size="40" color="#4CAF50"></sx-svg>
						<text class="title-text">快速记录</text>
					</view>
					<view class="record-buttons">
						<view class="record-btn" @click="goToWorkHours">
							<view class="btn-icon-wrapper btn-icon-blue">
								<sx-svg name="clock" :size="48" color="#2563eb"></sx-svg>
							</view>
							<view class="btn-content">
								<view class="btn-title">工时记录</view>
								<view class="btn-desc">时间段记录</view>
							</view>
						</view>
						<view class="record-btn" @click="goToPieceWork">
							<view class="btn-icon-wrapper btn-icon-green">
								<sx-svg name="package" :size="48" color="#16a34a"></sx-svg>
							</view>
							<view class="btn-content">
								<view class="btn-title">计件记录</view>
								<view class="btn-desc">数量统计</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 今日记录 -->
				<view class="today-records">
					<view class="section-header">
						<view class="section-title">
							<sx-svg name="list" :size="40" color="#2563eb"></sx-svg>
							<text class="title-text">今日记录</text>
						</view>
						<text class="record-count">({{ todayRecords.length }}条)</text>
					</view>

					<view class="records-list">
						<view v-for="record in todayRecords" :key="record.id" class="record-item">
							<view class="type-tag" :class="record.type === 'work-hours' ? 'type-tag-work' : 'type-tag-piece'">
								{{ record.typeText }}
							</view>
							<view class="record-content">
								<view class="record-left">
									<view class="record-icon" :class="record.type === 'work-hours' ? 'record-icon-blue' : 'record-icon-green'">
										<sx-svg :name="record.type === 'work-hours' ? 'clock' : 'package'" :size="32" :color="record.type === 'work-hours' ? '#2563eb' : '#16a34a'"></sx-svg>
									</view>
									<view class="record-info">
										<view class="record-title">{{ record.title }}</view>
										<view class="record-desc">{{ record.description }}</view>
									</view>
								</view>
								<view class="record-right">
									<view class="record-amount amount-text">¥{{ record.amount }}</view>
								</view>
							</view>
						</view>

						<!-- 空状态 -->
						<view v-if="todayRecords.length === 0" class="empty-state">
							<view class="empty-icon">
								<sx-svg name="plus" :size="128" color="#9CA3AF"></sx-svg>
							</view>
							<view class="empty-text">点击上方按钮添加新记录</view>
						</view>
					</view>
				</view>

				<!-- 底部安全区域 -->
				<view class="bottom-safe"></view>
			</view>
		</view>

		<!-- 悬浮添加按钮 -->
		<view class="fab-button" @click="showAddOptions">
			<sx-svg name="plus" :size="48" color="#ffffff"></sx-svg>
		</view>

		<!-- 底部导航 -->
		<tabbar></tabbar>
	</view>
</template>

<script setup>
	import {
		ref,
		onMounted
	} from 'vue'
	import tabbar from "@/components/common/l-tabbar.vue"
	import topNavbar from "@/components/common/top-navbar.vue"
	import sxSvg from "@/components/sx-svg/sx-svg.vue"
	import {
		useSystemInfo
	} from '@/hooks/useSystemInfo.js'

	// 隐藏系统tabbar
	uni.hideTabBar()

	// 使用系统信息 hook
	const {
		systemInfo,
		menuButtonInfo,
		navBarHeight,
		statusBarHeight,
		getSystemInfo
	} = useSystemInfo()

	// 响应式数据
	const todayIncome = ref({
		workHours: '128.00',
		pieceWork: '85.50',
		total: '213.50'
	})

	const growthRate = ref('12.5')

	const todayRecords = ref([
		{
			id: 1,
			type: 'work-hours',
			title: '09:00-12:00 工时',
			description: '3小时 · 数据录入',
			amount: '90.00',
			typeText: '工时'
		},
		{
			id: 2,
			type: 'work-hours',
			title: '14:00-17:00 工时',
			description: '3小时 · 文档整理',
			amount: '90.00',
			typeText: '工时'
		},
		{
			id: 3,
			type: 'piece-work',
			title: '计件×17个',
			description: '包装盒 · ¥5.00/个',
			amount: '85.00',
			typeText: '计件'
		}
	])


	// 页面方法

	const goToWorkHours = () => {
		uni.navigateTo({
			url: '/pages/work-hours/index'
		})
	}

	const goToPieceWork = () => {
		uni.navigateTo({
			url: '/pages/piece-work/index'
		})
	}

	const viewAllRecords = () => {
		uni.showToast({
			title: '查看全部记录',
			icon: 'none'
		})
	}

	const viewRecordDetail = (record) => {
		uni.showToast({
			title: `查看${record.title}详情`,
			icon: 'none'
		})
	}



	const showAddOptions = () => {
		uni.showActionSheet({
			itemList: ['工时记录', '计件记录'],
			success: (res) => {
				if (res.tapIndex === 0) {
					goToWorkHours()
				} else if (res.tapIndex === 1) {
					goToPieceWork()
				}
			}
		})
	}

	const handleTodayClick = () => {
		uni.showToast({
			title: '今天徽章点击',
			icon: 'none'
		})
	}

	// 页面生命周期
	onMounted(() => {
		// 获取系统信息
		getSystemInfo()

		// 模拟数据更新
		setInterval(() => {
			const currentTotal = parseFloat(todayIncome.value.total)
			const newTotal = currentTotal + (Math.random() * 10 - 5)
			if (newTotal > 0) {
				todayIncome.value.total = newTotal.toFixed(2)
			}
		}, 10000)
	})
</script>

<style lang="scss" scoped>
	/* 引入数字字体以增强数字显示效果 */
	/* #ifndef APP-NVUE */
	@font-face {
		font-family: 'DigitalNumbers';
		src: url('../../components/firstui/fui-number/numbers.ttf') format('truetype');
		font-weight: normal;
		font-style: normal;
	}

	/* #endif */

	.home-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #E8F5E9 0%, #F8F8F8 100%);
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
		padding-bottom: 160rpx;
	}

	.content-area {
		min-height: 100vh;
		padding: 0 32rpx 160rpx;
	}

	.income-overview {
		margin-bottom: 48rpx;

		.income-card {
			background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
			border-radius: 48rpx;
			padding: 48rpx;
			color: #ffffff;
			box-shadow: 0 16rpx 64rpx rgba(76, 175, 80, 0.3);
			transition: all 0.2s ease;

			&:active {
				box-shadow: 0 24rpx 80rpx rgba(76, 175, 80, 0.4);
				transform: translateY(-4rpx);
			}
		}

		.income-header {
			margin-bottom: 32rpx;

			.income-title {
				font-size: 32rpx;
				font-weight: 600;
				color: #ffffff;
			}
		}

		.income-details {
			margin-bottom: 32rpx;

			.income-item {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 24rpx;

				.income-label {
					font-size: 28rpx;
					color: rgba(255, 255, 255, 0.8);
					font-weight: 500;
				}

				.income-value {
					font-size: 36rpx;
					font-weight: 700;
					color: #ffffff;
					text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
					letter-spacing: 1rpx;

					&.amount-text {
						font-family: 'DigitalNumbers', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
					}
				}
			}
		}

		.income-divider {
			height: 2rpx;
			background: rgba(255, 255, 255, 0.3);
			margin: 24rpx 0;
			border-radius: 1rpx;
		}

		.income-total {
			.total-row {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 16rpx;

				.total-label {
					font-size: 32rpx;
					font-weight: 600;
					color: #ffffff;
				}

				.total-value {
					font-size: 60rpx;
					font-weight: 800;
					color: #ffffff;
					text-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.2);
					letter-spacing: 2rpx;
					position: relative;

					&.amount-text {
						font-family: 'DigitalNumbers', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
					}

					&::before {
						content: '';
						position: absolute;
						bottom: -4rpx;
						left: 0;
						right: 0;
						height: 3rpx;
						background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.6) 50%, transparent 100%);
						border-radius: 2rpx;
					}
				}
			}

			.growth-indicator {
				display: flex;
				align-items: center;
				justify-content: space-between;

				.growth-text {
					font-size: 28rpx;
					color: rgba(255, 255, 255, 0.8);
				}

				.growth-content {
					display: flex;
					align-items: center;
					gap: 8rpx;

					.growth-number {
						font-size: 28rpx;
						color: #ef4444;
						font-weight: 600;
					}
				}
			}
		}

	}

	.quick-record {
		margin-bottom: 48rpx;

		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 32rpx;
			padding: 0 8rpx;

			.title-text {
				font-size: 32rpx;
				font-weight: 600;
				color: #374151;
				margin-left: 16rpx;
			}
		}

		.record-buttons {
			display: grid;
			grid-template-columns: 1fr 1fr;
			gap: 32rpx;
		}

		.record-btn {
			background: rgba(255, 255, 255, 0.8);
			backdrop-filter: blur(20rpx);
			border: 2rpx solid rgba(255, 255, 255, 0.2);
			border-radius: 32rpx;
			padding: 32rpx;
			text-align: center;
			transition: all 0.15s ease;

			&:active {
				background: rgba(255, 255, 255, 0.9);
				transform: scale(0.95);
			}

			.btn-icon-wrapper {
				width: 96rpx;
				height: 96rpx;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin: 0 auto 24rpx;
			}

			.btn-icon-blue {
				background: #dbeafe;
			}

			.btn-icon-green {
				background: #dcfce7;
			}

			.btn-content {
				.btn-title {
					font-size: 32rpx;
					font-weight: 600;
					color: #374151;
					margin-bottom: 8rpx;
				}

				.btn-desc {
					font-size: 24rpx;
					color: #64748b;
				}
			}
		}
	}

	.today-records {
		margin-bottom: 48rpx;

		.section-header {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 32rpx;
			padding: 0 8rpx;

			.section-title {
				display: flex;
				align-items: center;

				.title-text {
					font-size: 32rpx;
					font-weight: 600;
					color: #1f2937;
					margin-left: 16rpx;
				}
			}

			.record-count {
				font-size: 28rpx;
				color: #6b7280;
			}
		}

		.records-list {
			.record-item {
				position: relative;
				background: rgba(255, 255, 255, 0.9);
				backdrop-filter: blur(20rpx);
				border: 2rpx solid rgba(255, 255, 255, 0.2);
				border-radius: 32rpx;
				padding: 32rpx;
				margin-bottom: 24rpx;
				box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
				transition: all 0.2s ease;

				&:active {
					box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.12);
					transform: translateY(-4rpx);
				}

				.type-tag {
					position: absolute;
					top: 0;
					right: 0;
					padding: 8rpx 16rpx 8rpx 20rpx;
					font-size: 20rpx;
					font-weight: 600;
					line-height: 1.2;
					z-index: 10;
					border-radius: 0 32rpx 0 20rpx;
				}

				.type-tag-work {
					background: rgba(37, 99, 235, 0.1);
					color: #2563eb;
				}

				.type-tag-piece {
					background: rgba(22, 163, 74, 0.1);
					color: #16a34a;
				}

				.record-content {
					display: flex;
					align-items: center;
					justify-content: space-between;

					.record-left {
						display: flex;
						align-items: center;
						flex: 1;

						.record-icon {
							width: 64rpx;
							height: 64rpx;
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-right: 24rpx;
						}

						.record-icon-blue {
							background: #dbeafe;
						}

						.record-icon-green {
							background: #dcfce7;
						}

						.record-info {
							flex: 1;

							.record-title {
								font-size: 32rpx;
								font-weight: 600;
								color: #1f2937;
								margin-bottom: 8rpx;
							}

							.record-desc {
								font-size: 28rpx;
								color: #6b7280;
							}
						}
					}

					.record-right {
						text-align: right;

						.record-amount {
							font-size: 32rpx;
							font-weight: 600;
							color: #10b981;

							&.amount-text {
								font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
							}
						}
					}
				}
			}

			.empty-state {
				background: rgba(255, 255, 255, 0.9);
				backdrop-filter: blur(20rpx);
				border: 2rpx solid rgba(255, 255, 255, 0.2);
				border-radius: 32rpx;
				padding: 96rpx 48rpx;
				text-align: center;

				.empty-icon {
					margin-bottom: 32rpx;
					opacity: 0.5;
				}

				.empty-text {
					font-size: 28rpx;
					color: #6b7280;
				}
			}
		}
	}

	.fab-button {
		position: fixed;
		bottom: 200rpx;
		right: 40rpx;
		width: 112rpx;
		height: 112rpx;
		background: linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		box-shadow: 0 16rpx 48rpx rgba(76, 175, 80, 0.3);
		z-index: 1000;
		transition: all 0.15s ease;

		&:active {
			transform: scale(0.95);
			box-shadow: 0 8rpx 24rpx rgba(76, 175, 80, 0.2);
		}

		&:hover {
			box-shadow: 0 24rpx 64rpx rgba(76, 175, 80, 0.4);
		}
	}

	.bottom-safe {
		height: 40rpx;
	}

	// 通用样式类
	.amount-text {
		font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
		font-weight: 600;
	}

	// 卡片通用样式
	.card {
		background: rgba(255, 255, 255, 0.9);
		backdrop-filter: blur(20rpx);
		border: 2rpx solid rgba(255, 255, 255, 0.2);
		box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.08);
		transition: all 0.2s ease;
	}

	.card:active {
		box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.12);
		transform: translateY(-4rpx);
	}
</style>